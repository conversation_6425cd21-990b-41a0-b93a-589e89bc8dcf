package cache

import (
	"fmt"
	"remnawave-tg-shop-bot/internal/fsm"
	"strconv"
	"time"
)

// CacheHelper предоставляет удобные методы для работы с кешем
type CacheHelper struct {
	cache *Cache
}

// NewCacheHelper создает новый экземпляр CacheHelper
func NewCacheHelper(cache *Cache) *CacheHelper {
	return &CacheHelper{
		cache: cache,
	}
}

// --- Методы для работы с временными данными ---

// SetTempString сохраняет временную строку для пользователя
func (h *CacheHelper) SetTempString(userID int64, keyType fsm.CacheKeyType, value string) {
	key := fsm.GetCacheKey(userID, keyType)
	h.cache.SetString(key, value)
}

// GetTempString получает временную строку для пользователя
func (h *CacheHelper) GetTempString(userID int64, keyType fsm.CacheKeyType) (string, bool) {
	key := fsm.GetCacheKey(userID, keyType)
	return h.cache.GetString(key)
}

// SetTempInt сохраняет временное число для пользователя
func (h *CacheHelper) SetTempInt(userID int64, keyType fsm.CacheKeyType, value int) {
	key := fsm.GetCacheKey(userID, keyType)
	h.cache.SetInt(key, value)
}

// GetTempInt получает временное число для пользователя
func (h *CacheHelper) GetTempInt(userID int64, keyType fsm.CacheKeyType) (int, bool) {
	key := fsm.GetCacheKey(userID, keyType)
	return h.cache.GetInt(key)
}

// DeleteTemp удаляет временные данные для пользователя
func (h *CacheHelper) DeleteTemp(userID int64, keyType fsm.CacheKeyType) {
	key := fsm.GetCacheKey(userID, keyType)
	h.cache.Delete(key)
}

// --- Методы для работы с сообщениями ---

// SetLastMessageID сохраняет ID последнего сообщения для пользователя
func (h *CacheHelper) SetLastMessageID(userID int64, messageID int) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyLastMsg)
	h.cache.SetInt(key, messageID)
}

// GetLastMessageID получает ID последнего сообщения для пользователя
func (h *CacheHelper) GetLastMessageID(userID int64) (int, bool) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyLastMsg)
	return h.cache.GetInt(key)
}

// SetBroadcastMessageID сохраняет ID сообщения рассылки для пользователя
func (h *CacheHelper) SetBroadcastMessageID(userID int64, messageID int) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyBroadcastMsg)
	h.cache.SetInt(key, messageID)
}

// GetBroadcastMessageID получает ID сообщения рассылки для пользователя
func (h *CacheHelper) GetBroadcastMessageID(userID int64) (int, bool) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyBroadcastMsg)
	return h.cache.GetInt(key)
}

// --- Методы для работы с данными рассылок ---

// SetBroadcastText сохраняет текст рассылки для пользователя
func (h *CacheHelper) SetBroadcastText(userID int64, text string) {
	h.SetTempString(userID, fsm.CacheKeyTempData1, text)
}

// GetBroadcastText получает текст рассылки для пользователя
func (h *CacheHelper) GetBroadcastText(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData1)
}

// SetBroadcastTime сохраняет время рассылки для пользователя
func (h *CacheHelper) SetBroadcastTime(userID int64, timeStr string) {
	h.SetTempString(userID, fsm.CacheKeyTempData2, timeStr)
}

// GetBroadcastTime получает время рассылки для пользователя
func (h *CacheHelper) GetBroadcastTime(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData2)
}

// SetBroadcastAudience сохраняет аудиторию рассылки для пользователя
func (h *CacheHelper) SetBroadcastAudience(userID int64, audience string) {
	h.SetTempString(userID, fsm.CacheKeyTempData3, audience)
}

// GetBroadcastAudience получает аудиторию рассылки для пользователя
func (h *CacheHelper) GetBroadcastAudience(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData3)
}

// --- Методы для редактирования рассылок ---

// SetBroadcastEditText сохраняет текст редактируемой рассылки для пользователя
func (h *CacheHelper) SetBroadcastEditText(userID int64, text string) {
	h.SetTempString(userID, fsm.CacheKeyTempData5, text)
}

// GetBroadcastEditText получает текст редактируемой рассылки для пользователя
func (h *CacheHelper) GetBroadcastEditText(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData5)
}

// SetBroadcastEditTime сохраняет время редактируемой рассылки для пользователя
func (h *CacheHelper) SetBroadcastEditTime(userID int64, timeStr string) {
	h.SetTempString(userID, fsm.CacheKeyTempData6, timeStr)
}

// GetBroadcastEditTime получает время редактируемой рассылки для пользователя
func (h *CacheHelper) GetBroadcastEditTime(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData6)
}

// SetBroadcastEditID сохраняет ID редактируемой рассылки для пользователя
func (h *CacheHelper) SetBroadcastEditID(userID int64, id int64) {
	h.SetTempInt(userID, fsm.CacheKeyTempData8, int(id))
}

// GetBroadcastEditID получает ID редактируемой рассылки для пользователя
func (h *CacheHelper) GetBroadcastEditID(userID int64) (int64, bool) {
	id, ok := h.GetTempInt(userID, fsm.CacheKeyTempData8)
	return int64(id), ok
}

// ClearBroadcastEditData очищает все данные редактирования рассылки для пользователя
func (h *CacheHelper) ClearBroadcastEditData(userID int64) {
	h.cache.Delete(fsm.GetCacheKey(userID, fsm.CacheKeyTempData5))
	h.cache.Delete(fsm.GetCacheKey(userID, fsm.CacheKeyTempData6))
	h.cache.Delete(fsm.GetCacheKey(userID, fsm.CacheKeyTempData8))
}

// --- Методы для работы с данными тарифов ---

// SetTariffCode сохраняет код тарифа для пользователя
func (h *CacheHelper) SetTariffCode(userID int64, code string) {
	h.SetTempString(userID, fsm.CacheKeyTempData7, code)
}

// GetTariffCode получает код тарифа для пользователя
func (h *CacheHelper) GetTariffCode(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData7)
}

// SetTariffTitle сохраняет название тарифа для пользователя
func (h *CacheHelper) SetTariffTitle(userID int64, title string) {
	h.SetTempString(userID, fsm.CacheKeyTempData7_1, title)
}

// GetTariffTitle получает название тарифа для пользователя
func (h *CacheHelper) GetTariffTitle(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData7_1)
}

// SetTariffPriceRUB сохраняет цену тарифа в рублях для пользователя
func (h *CacheHelper) SetTariffPriceRUB(userID int64, price int) {
	key := 7000002 + userID // Временно используем старый ключ
	h.cache.SetInt(key, price)
}

// GetTariffPriceRUB получает цену тарифа в рублях для пользователя
func (h *CacheHelper) GetTariffPriceRUB(userID int64) (int, bool) {
	key := 7000002 + userID // Временно используем старый ключ
	return h.cache.GetInt(key)
}

// SetTariffPriceStars сохраняет цену тарифа в звездах для пользователя
func (h *CacheHelper) SetTariffPriceStars(userID int64, price int) {
	key := 7000003 + userID // Временно используем старый ключ
	h.cache.SetInt(key, price)
}

// GetTariffPriceStars получает цену тарифа в звездах для пользователя
func (h *CacheHelper) GetTariffPriceStars(userID int64) (int, bool) {
	key := 7000003 + userID // Временно используем старый ключ
	return h.cache.GetInt(key)
}

// SetTariffActive сохраняет статус активности тарифа
func (h *CacheHelper) SetTariffActive(userID int64, active int) {
	h.cache.SetInt(7000004+userID, active)
}

// GetTariffActive получает статус активности тарифа
func (h *CacheHelper) GetTariffActive(userID int64) (int, bool) {
	return h.cache.GetInt(7000004 + userID)
}

// --- Методы для очистки данных ---

// ClearBroadcastData очищает все временные данные рассылки для пользователя
func (h *CacheHelper) ClearBroadcastData(userID int64) {
	h.DeleteTemp(userID, fsm.CacheKeyTempData1)
	h.DeleteTemp(userID, fsm.CacheKeyTempData2)
	h.DeleteTemp(userID, fsm.CacheKeyTempData3)
	h.DeleteTemp(userID, fsm.CacheKeyTempData4)
	h.DeleteTemp(userID, fsm.CacheKeyBroadcastMsg)
	h.DeleteTemp(userID, fsm.CacheKeyBroadcastMsg2)
	h.DeleteTemp(userID, fsm.CacheKeyTempMsg)
	h.DeleteTemp(userID, fsm.CacheKeyTempData9)
}

// ClearTariffData очищает все временные данные тарифа для пользователя
func (h *CacheHelper) ClearTariffData(userID int64) {
	h.DeleteTemp(userID, fsm.CacheKeyTempData7)
	h.DeleteTemp(userID, fsm.CacheKeyTempData7_1)
	// Очищаем старые ключи
	h.cache.Delete(7000002 + userID)
	h.cache.Delete(7000003 + userID)
	h.cache.Delete(7000004 + userID)
}

// ClearAllUserData очищает все временные данные для пользователя
func (h *CacheHelper) ClearAllUserData(userID int64) {
	h.ClearBroadcastData(userID)
	h.ClearTariffData(userID)
	h.DeleteTemp(userID, fsm.CacheKeyLastMsg)
}

// --- Утилитарные методы ---

// SetWithExpiration сохраняет значение с истечением срока действия
func (h *CacheHelper) SetWithExpiration(userID int64, keyType fsm.CacheKeyType, value string, expiration time.Duration) {
	key := fsm.GetCacheKey(userID, keyType)
	// Базовый Cache не поддерживает expiration, но можно расширить
	h.cache.SetString(key, value)
	// TODO: Добавить поддержку expiration в базовый Cache
}

// GetUserDataSummary возвращает сводку данных пользователя для отладки
func (h *CacheHelper) GetUserDataSummary(userID int64) map[string]interface{} {
	summary := make(map[string]interface{})

	// FSM состояние
	if state, ok := h.cache.GetInt(userID); ok {
		summary["fsm_state"] = state
	}

	// Данные рассылок
	if text, ok := h.GetBroadcastText(userID); ok {
		summary["broadcast_text"] = text
	}
	if time, ok := h.GetBroadcastTime(userID); ok {
		summary["broadcast_time"] = time
	}
	if audience, ok := h.GetBroadcastAudience(userID); ok {
		summary["broadcast_audience"] = audience
	}

	// Данные тарифов
	if code, ok := h.GetTariffCode(userID); ok {
		summary["tariff_code"] = code
	}
	if title, ok := h.GetTariffTitle(userID); ok {
		summary["tariff_title"] = title
	}
	if price, ok := h.GetTariffPriceRUB(userID); ok {
		summary["tariff_price_rub"] = price
	}
	if price, ok := h.GetTariffPriceStars(userID); ok {
		summary["tariff_price_stars"] = price
	}

	// ID сообщений
	if msgID, ok := h.GetLastMessageID(userID); ok {
		summary["last_message_id"] = msgID
	}
	if msgID, ok := h.GetBroadcastMessageID(userID); ok {
		summary["broadcast_message_id"] = msgID
	}

	return summary
}

// ParseIntFromString безопасно парсит строку в число
func (h *CacheHelper) ParseIntFromString(s string) (int, error) {
	return strconv.Atoi(s)
}

// FormatIntToString форматирует число в строку
func (h *CacheHelper) FormatIntToString(i int) string {
	return strconv.Itoa(i)
}

// ValidateUserID проверяет корректность ID пользователя
func (h *CacheHelper) ValidateUserID(userID int64) error {
	if userID <= 0 {
		return fmt.Errorf("invalid user ID: %d", userID)
	}
	return nil
}

// --- Управление сообщениями ---

// SetBroadcastMessages сохраняет список ID сообщений рассылки
func (h *CacheHelper) SetBroadcastMessages(userID int64, messageIDs []int) {
	h.cache.Set(5000000+userID, messageIDs)
}

// GetBroadcastMessages получает список ID сообщений рассылки
func (h *CacheHelper) GetBroadcastMessages(userID int64) ([]int, bool) {
	if value, ok := h.cache.Get(5000000 + userID); ok {
		if ids, ok := value.([]int); ok {
			return ids, true
		}
	}
	return nil, false
}

// ClearBroadcastMessages очищает список ID сообщений рассылки
func (h *CacheHelper) ClearBroadcastMessages(userID int64) {
	h.cache.Delete(5000000 + userID)
}

// SetBroadcastConfirmMessageID устанавливает ID сообщения подтверждения рассылки
func (h *CacheHelper) SetBroadcastConfirmMessageID(userID int64, messageID int) {
	h.cache.Set(6000000+userID, messageID)
}

// GetBroadcastConfirmMessageID получает ID сообщения подтверждения рассылки
func (h *CacheHelper) GetBroadcastConfirmMessageID(userID int64) (int, bool) {
	return h.cache.GetInt(6000000 + userID)
}

// SetTempMessageID устанавливает временный ID сообщения
func (h *CacheHelper) SetTempMessageID(userID int64, messageID int) {
	h.cache.SetInt(9999999+userID, messageID)
}

// GetTempMessageID получает временный ID сообщения
func (h *CacheHelper) GetTempMessageID(userID int64) (int, bool) {
	return h.cache.GetInt(9999999 + userID)
}

// SetTempBroadcastText устанавливает временный текст рассылки
func (h *CacheHelper) SetTempBroadcastText(userID int64, text string) {
	h.cache.SetString(1000000+userID, text)
}

// GetTempBroadcastText получает временный текст рассылки
func (h *CacheHelper) GetTempBroadcastText(userID int64) (string, bool) {
	return h.cache.GetString(1000000 + userID)
}

// SetTempBroadcastTime устанавливает временное время рассылки
func (h *CacheHelper) SetTempBroadcastTime(userID int64, timeStr string) {
	h.cache.SetString(4000000+userID, timeStr)
}

// GetTempBroadcastTime получает временное время рассылки
func (h *CacheHelper) GetTempBroadcastTime(userID int64) (string, bool) {
	return h.cache.GetString(4000000 + userID)
}

// SetTempEditText устанавливает временный текст редактирования
func (h *CacheHelper) SetTempEditText(userID int64, text string) {
	h.cache.SetString(3000000+userID, text)
}

// GetTempEditText получает временный текст редактирования
func (h *CacheHelper) GetTempEditText(userID int64) (string, bool) {
	return h.cache.GetString(3000000 + userID)
}

// DeleteMessagesExcept удаляет сообщения из списка, исключая указанные ID
func (h *CacheHelper) DeleteMessagesExcept(messageIDs []int, exceptIDs []int) []int {
	var result []int
	for _, id := range messageIDs {
		skip := false
		for _, except := range exceptIDs {
			if id == except {
				skip = true
				break
			}
		}
		if !skip {
			result = append(result, id)
		}
	}
	return result
}

// SetTariffMessages сохраняет список ID сообщений тарифов
func (h *CacheHelper) SetTariffMessages(userID int64, messageIDs []int) {
	h.cache.Set(5100000+userID, messageIDs)
}

// GetTariffMessages получает список ID сообщений тарифов
func (h *CacheHelper) GetTariffMessages(userID int64) ([]int, bool) {
	if value, ok := h.cache.Get(5100000 + userID); ok {
		if ids, ok := value.([]int); ok {
			return ids, true
		}
	}
	return nil, false
}

// ClearTariffMessages очищает список ID сообщений тарифов
func (h *CacheHelper) ClearTariffMessages(userID int64) {
	h.cache.Delete(5100000 + userID)
}

// --- Управление последним сообщением ---

// ClearLastMessageID очищает ID последнего сообщения
func (h *CacheHelper) ClearLastMessageID(userID int64) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyLastMsg)
	h.cache.Delete(key)
}

// --- Управление instant broadcast ---

// SetInstantBroadcastText сохраняет текст моментальной рассылки
func (h *CacheHelper) SetInstantBroadcastText(userID int64, text string) {
	h.cache.SetString(9000000+userID, text)
}

// GetInstantBroadcastText получает текст моментальной рассылки
func (h *CacheHelper) GetInstantBroadcastText(userID int64) (string, bool) {
	return h.cache.GetString(9000000 + userID)
}

// ClearInstantBroadcastText очищает текст моментальной рассылки
func (h *CacheHelper) ClearInstantBroadcastText(userID int64) {
	h.cache.Delete(9000000 + userID)
}
