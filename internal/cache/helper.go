package cache

import (
	"fmt"
	"remnawave-tg-shop-bot/internal/fsm"
	"strconv"
	"time"
)

// CacheHelper предоставляет удобные методы для работы с кешем
type CacheHelper struct {
	cache *Cache
}

// NewCacheHelper создает новый экземпляр CacheHelper
func NewCacheHelper(cache *Cache) *CacheHelper {
	return &CacheHelper{
		cache: cache,
	}
}

// --- Методы для работы с временными данными ---

// SetTempString сохраняет временную строку для пользователя
func (h *CacheHelper) SetTempString(userID int64, keyType fsm.CacheKeyType, value string) {
	key := fsm.GetCacheKey(userID, keyType)
	h.cache.SetString(key, value)
}

// GetTempString получает временную строку для пользователя
func (h *CacheHelper) GetTempString(userID int64, keyType fsm.CacheKeyType) (string, bool) {
	key := fsm.GetCacheKey(userID, keyType)
	return h.cache.GetString(key)
}

// SetTempInt сохраняет временное число для пользователя
func (h *CacheHelper) SetTempInt(userID int64, keyType fsm.CacheKeyType, value int) {
	key := fsm.GetCacheKey(userID, keyType)
	h.cache.SetInt(key, value)
}

// GetTempInt получает временное число для пользователя
func (h *CacheHelper) GetTempInt(userID int64, keyType fsm.CacheKeyType) (int, bool) {
	key := fsm.GetCacheKey(userID, keyType)
	return h.cache.GetInt(key)
}

// DeleteTemp удаляет временные данные для пользователя
func (h *CacheHelper) DeleteTemp(userID int64, keyType fsm.CacheKeyType) {
	key := fsm.GetCacheKey(userID, keyType)
	h.cache.Delete(key)
}

// --- Методы для работы с сообщениями ---

// SetLastMessageID сохраняет ID последнего сообщения для пользователя
func (h *CacheHelper) SetLastMessageID(userID int64, messageID int) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyLastMsg)
	h.cache.SetInt(key, messageID)
}

// GetLastMessageID получает ID последнего сообщения для пользователя
func (h *CacheHelper) GetLastMessageID(userID int64) (int, bool) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyLastMsg)
	return h.cache.GetInt(key)
}

// SetBroadcastMessageID сохраняет ID сообщения рассылки для пользователя
func (h *CacheHelper) SetBroadcastMessageID(userID int64, messageID int) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyBroadcastMsg)
	h.cache.SetInt(key, messageID)
}

// GetBroadcastMessageID получает ID сообщения рассылки для пользователя
func (h *CacheHelper) GetBroadcastMessageID(userID int64) (int, bool) {
	key := fsm.GetCacheKey(userID, fsm.CacheKeyBroadcastMsg)
	return h.cache.GetInt(key)
}

// --- Методы для работы с данными рассылок ---

// SetBroadcastText сохраняет текст рассылки для пользователя
func (h *CacheHelper) SetBroadcastText(userID int64, text string) {
	h.SetTempString(userID, fsm.CacheKeyTempData1, text)
}

// GetBroadcastText получает текст рассылки для пользователя
func (h *CacheHelper) GetBroadcastText(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData1)
}

// SetBroadcastTime сохраняет время рассылки для пользователя
func (h *CacheHelper) SetBroadcastTime(userID int64, timeStr string) {
	h.SetTempString(userID, fsm.CacheKeyTempData2, timeStr)
}

// GetBroadcastTime получает время рассылки для пользователя
func (h *CacheHelper) GetBroadcastTime(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData2)
}

// SetBroadcastAudience сохраняет аудиторию рассылки для пользователя
func (h *CacheHelper) SetBroadcastAudience(userID int64, audience string) {
	h.SetTempString(userID, fsm.CacheKeyTempData3, audience)
}

// GetBroadcastAudience получает аудиторию рассылки для пользователя
func (h *CacheHelper) GetBroadcastAudience(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData3)
}

// --- Методы для работы с данными тарифов ---

// SetTariffCode сохраняет код тарифа для пользователя
func (h *CacheHelper) SetTariffCode(userID int64, code string) {
	h.SetTempString(userID, fsm.CacheKeyTempData7, code)
}

// GetTariffCode получает код тарифа для пользователя
func (h *CacheHelper) GetTariffCode(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData7)
}

// SetTariffTitle сохраняет название тарифа для пользователя
func (h *CacheHelper) SetTariffTitle(userID int64, title string) {
	h.SetTempString(userID, fsm.CacheKeyTempData7_1, title)
}

// GetTariffTitle получает название тарифа для пользователя
func (h *CacheHelper) GetTariffTitle(userID int64) (string, bool) {
	return h.GetTempString(userID, fsm.CacheKeyTempData7_1)
}

// SetTariffPriceRUB сохраняет цену тарифа в рублях для пользователя
func (h *CacheHelper) SetTariffPriceRUB(userID int64, price int) {
	key := 7000002 + userID // Временно используем старый ключ
	h.cache.SetInt(key, price)
}

// GetTariffPriceRUB получает цену тарифа в рублях для пользователя
func (h *CacheHelper) GetTariffPriceRUB(userID int64) (int, bool) {
	key := 7000002 + userID // Временно используем старый ключ
	return h.cache.GetInt(key)
}

// SetTariffPriceStars сохраняет цену тарифа в звездах для пользователя
func (h *CacheHelper) SetTariffPriceStars(userID int64, price int) {
	key := 7000003 + userID // Временно используем старый ключ
	h.cache.SetInt(key, price)
}

// GetTariffPriceStars получает цену тарифа в звездах для пользователя
func (h *CacheHelper) GetTariffPriceStars(userID int64) (int, bool) {
	key := 7000003 + userID // Временно используем старый ключ
	return h.cache.GetInt(key)
}

// --- Методы для очистки данных ---

// ClearBroadcastData очищает все временные данные рассылки для пользователя
func (h *CacheHelper) ClearBroadcastData(userID int64) {
	h.DeleteTemp(userID, fsm.CacheKeyTempData1)
	h.DeleteTemp(userID, fsm.CacheKeyTempData2)
	h.DeleteTemp(userID, fsm.CacheKeyTempData3)
	h.DeleteTemp(userID, fsm.CacheKeyTempData4)
	h.DeleteTemp(userID, fsm.CacheKeyBroadcastMsg)
	h.DeleteTemp(userID, fsm.CacheKeyBroadcastMsg2)
	h.DeleteTemp(userID, fsm.CacheKeyTempMsg)
	h.DeleteTemp(userID, fsm.CacheKeyTempData9)
}

// ClearTariffData очищает все временные данные тарифа для пользователя
func (h *CacheHelper) ClearTariffData(userID int64) {
	h.DeleteTemp(userID, fsm.CacheKeyTempData7)
	h.DeleteTemp(userID, fsm.CacheKeyTempData7_1)
	// Очищаем старые ключи
	h.cache.Delete(7000002 + userID)
	h.cache.Delete(7000003 + userID)
	h.cache.Delete(7000004 + userID)
}

// ClearAllUserData очищает все временные данные для пользователя
func (h *CacheHelper) ClearAllUserData(userID int64) {
	h.ClearBroadcastData(userID)
	h.ClearTariffData(userID)
	h.DeleteTemp(userID, fsm.CacheKeyLastMsg)
}

// --- Утилитарные методы ---

// SetWithExpiration сохраняет значение с истечением срока действия
func (h *CacheHelper) SetWithExpiration(userID int64, keyType fsm.CacheKeyType, value string, expiration time.Duration) {
	key := fsm.GetCacheKey(userID, keyType)
	// Базовый Cache не поддерживает expiration, но можно расширить
	h.cache.SetString(key, value)
	// TODO: Добавить поддержку expiration в базовый Cache
}

// GetUserDataSummary возвращает сводку данных пользователя для отладки
func (h *CacheHelper) GetUserDataSummary(userID int64) map[string]interface{} {
	summary := make(map[string]interface{})
	
	// FSM состояние
	if state, ok := h.cache.GetInt(userID); ok {
		summary["fsm_state"] = state
	}
	
	// Данные рассылок
	if text, ok := h.GetBroadcastText(userID); ok {
		summary["broadcast_text"] = text
	}
	if time, ok := h.GetBroadcastTime(userID); ok {
		summary["broadcast_time"] = time
	}
	if audience, ok := h.GetBroadcastAudience(userID); ok {
		summary["broadcast_audience"] = audience
	}
	
	// Данные тарифов
	if code, ok := h.GetTariffCode(userID); ok {
		summary["tariff_code"] = code
	}
	if title, ok := h.GetTariffTitle(userID); ok {
		summary["tariff_title"] = title
	}
	if price, ok := h.GetTariffPriceRUB(userID); ok {
		summary["tariff_price_rub"] = price
	}
	if price, ok := h.GetTariffPriceStars(userID); ok {
		summary["tariff_price_stars"] = price
	}
	
	// ID сообщений
	if msgID, ok := h.GetLastMessageID(userID); ok {
		summary["last_message_id"] = msgID
	}
	if msgID, ok := h.GetBroadcastMessageID(userID); ok {
		summary["broadcast_message_id"] = msgID
	}
	
	return summary
}

// ParseIntFromString безопасно парсит строку в число
func (h *CacheHelper) ParseIntFromString(s string) (int, error) {
	return strconv.Atoi(s)
}

// FormatIntToString форматирует число в строку
func (h *CacheHelper) FormatIntToString(i int) string {
	return strconv.Itoa(i)
}

// ValidateUserID проверяет корректность ID пользователя
func (h *CacheHelper) ValidateUserID(userID int64) error {
	if userID <= 0 {
		return fmt.Errorf("invalid user ID: %d", userID)
	}
	return nil
}
