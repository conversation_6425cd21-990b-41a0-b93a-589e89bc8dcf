package cache

import (
	"remnawave-tg-shop-bot/internal/fsm"
	"time"
)

// CacheHelperInterface определяет интерфейс для работы с кешем
type CacheHelperInterface interface {
	// --- Методы для работы с временными данными ---
	SetTempString(userID int64, keyType fsm.CacheKeyType, value string)
	GetTempString(userID int64, keyType fsm.CacheKeyType) (string, bool)
	SetTempInt(userID int64, keyType fsm.CacheKeyType, value int)
	GetTempInt(userID int64, keyType fsm.CacheKeyType) (int, bool)
	DeleteTemp(userID int64, keyType fsm.CacheKeyType)

	// --- Методы для работы с сообщениями ---
	SetBroadcastMessageID(userID int64, messageID int)
	GetBroadcastMessageID(userID int64) (int, bool)

	// --- Методы для работы с данными рассылок ---
	SetBroadcastText(userID int64, text string)
	GetBroadcastText(userID int64) (string, bool)
	SetBroadcastTime(userID int64, timeStr string)
	GetBroadcastTime(userID int64) (string, bool)
	SetBroadcastAudience(userID int64, audience string)
	GetBroadcastAudience(userID int64) (string, bool)

	// --- Методы для редактирования рассылок ---
	SetBroadcastEditText(userID int64, text string)
	GetBroadcastEditText(userID int64) (string, bool)
	SetBroadcastEditTime(userID int64, timeStr string)
	GetBroadcastEditTime(userID int64) (string, bool)
	SetBroadcastEditID(userID int64, id int64)
	GetBroadcastEditID(userID int64) (int64, bool)
	ClearBroadcastEditData(userID int64)

	// --- Управление сообщениями ---
	SetBroadcastMessages(userID int64, messageIDs []int)
	GetBroadcastMessages(userID int64) ([]int, bool)
	ClearBroadcastMessages(userID int64)

	// --- Управление сообщениями подтверждения ---
	SetBroadcastConfirmMessageID(userID int64, messageID int)
	GetBroadcastConfirmMessageID(userID int64) (int, bool)

	SetTariffMessages(userID int64, messageIDs []int)
	GetTariffMessages(userID int64) ([]int, bool)
	ClearTariffMessages(userID int64)

	// --- Управление последним сообщением ---
	SetLastMessageID(userID int64, messageID int)
	GetLastMessageID(userID int64) (int, bool)
	ClearLastMessageID(userID int64)

	// --- Управление instant broadcast ---
	SetInstantBroadcastText(userID int64, text string)
	GetInstantBroadcastText(userID int64) (string, bool)
	ClearInstantBroadcastText(userID int64)

	// --- Управление временными данными FSM ---
	SetTempMessageID(userID int64, messageID int)
	GetTempMessageID(userID int64) (int, bool)
	SetTempBroadcastText(userID int64, text string)
	GetTempBroadcastText(userID int64) (string, bool)
	SetTempBroadcastTime(userID int64, timeStr string)
	GetTempBroadcastTime(userID int64) (string, bool)
	SetTempEditText(userID int64, text string)
	GetTempEditText(userID int64) (string, bool)

	// --- Методы для работы с данными тарифов ---
	SetTariffCode(userID int64, code string)
	GetTariffCode(userID int64) (string, bool)
	SetTariffTitle(userID int64, title string)
	GetTariffTitle(userID int64) (string, bool)
	SetTariffPriceRUB(userID int64, price int)
	GetTariffPriceRUB(userID int64) (int, bool)
	SetTariffPriceStars(userID int64, price int)
	GetTariffPriceStars(userID int64) (int, bool)
	SetTariffActive(userID int64, active int)
	GetTariffActive(userID int64) (int, bool)

	// --- Методы для очистки данных ---
	ClearBroadcastData(userID int64)
	ClearTariffData(userID int64)
	ClearAllUserData(userID int64)

	// --- Утилитарные методы ---
	SetWithExpiration(userID int64, keyType fsm.CacheKeyType, value string, expiration time.Duration)
	GetUserDataSummary(userID int64) map[string]interface{}
	ParseIntFromString(s string) (int, error)
	FormatIntToString(i int) string
	ValidateUserID(userID int64) error
}

// Убеждаемся, что CacheHelper реализует интерфейс CacheHelperInterface
var _ CacheHelperInterface = (*CacheHelper)(nil)
