package fsm

// Константы для ключей кеша, чтобы избежать магических чисел
const (
	// Базовые смещения для разных типов данных
	CacheOffsetTempData1     = 1000000 // Временные данные тип 1
	CacheOffsetTempData2     = 2000000 // Временные данные тип 2
	CacheOffsetTempData3     = 3000000 // Временные данные тип 3
	CacheOffsetTempData4     = 4000000 // Временные данные тип 4
	CacheOffsetTempData5     = 5000000 // Временные данные тип 5 (редактирование рассылок - текст)
	CacheOffsetTempData6     = 6000000 // Временные данные тип 6 (редактирование рассылок - время)
	CacheOffsetBroadcastMsg  = 5000000 // ID сообщений рассылок
	CacheOffsetBroadcastMsg2 = 5100000 // ID сообщений рассылок (дополнительные)
	CacheOffsetTempMsg       = 6000000 // Временные сообщения
	CacheOffsetTempData7     = 7000000 // Временные данные тип 7
	CacheOffsetTempData7_1   = 7000001 // Временные данные тип 7 (дополнительные)
	CacheOffsetTempData9     = 9000000 // Временные данные тип 9
	CacheOffsetLastMsg       = 9999999 // ID последнего сообщения
)

// CacheKeyType представляет тип ключа кеша
type CacheKeyType int

const (
	CacheKeyTempData1 CacheKeyType = iota
	CacheKeyTempData2
	CacheKeyTempData3
	CacheKeyTempData4
	CacheKeyTempData5
	CacheKeyTempData6
	CacheKeyTempData8
	CacheKeyBroadcastMsg
	CacheKeyBroadcastMsg2
	CacheKeyTempMsg
	CacheKeyTempData7
	CacheKeyTempData7_1
	CacheKeyTempData9
	CacheKeyLastMsg
)

// GetCacheKey возвращает ключ кеша для пользователя и типа данных
func GetCacheKey(userID int64, keyType CacheKeyType) int64 {
	switch keyType {
	case CacheKeyTempData1:
		return CacheOffsetTempData1 + userID
	case CacheKeyTempData2:
		return CacheOffsetTempData2 + userID
	case CacheKeyTempData3:
		return CacheOffsetTempData3 + userID
	case CacheKeyTempData4:
		return CacheOffsetTempData4 + userID
	case CacheKeyTempData5:
		return CacheOffsetTempData5 + userID
	case CacheKeyTempData6:
		return CacheOffsetTempData6 + userID
	case CacheKeyTempData8:
		return CacheOffsetTempData7 + userID // Используем тот же offset что и для TempData7
	case CacheKeyBroadcastMsg:
		return CacheOffsetBroadcastMsg + userID
	case CacheKeyBroadcastMsg2:
		return CacheOffsetBroadcastMsg2 + userID
	case CacheKeyTempMsg:
		return CacheOffsetTempMsg + userID
	case CacheKeyTempData7:
		return CacheOffsetTempData7 + userID
	case CacheKeyTempData7_1:
		return CacheOffsetTempData7_1 + userID
	case CacheKeyTempData9:
		return CacheOffsetTempData9 + userID
	case CacheKeyLastMsg:
		return CacheOffsetLastMsg + userID
	default:
		return userID
	}
}

// String возвращает строковое представление типа ключа кеша
func (c CacheKeyType) String() string {
	switch c {
	case CacheKeyTempData1:
		return "TempData1"
	case CacheKeyTempData2:
		return "TempData2"
	case CacheKeyTempData3:
		return "TempData3"
	case CacheKeyTempData4:
		return "TempData4"
	case CacheKeyTempData5:
		return "TempData5"
	case CacheKeyTempData6:
		return "TempData6"
	case CacheKeyTempData8:
		return "TempData8"
	case CacheKeyBroadcastMsg:
		return "BroadcastMsg"
	case CacheKeyBroadcastMsg2:
		return "BroadcastMsg2"
	case CacheKeyTempMsg:
		return "TempMsg"
	case CacheKeyTempData7:
		return "TempData7"
	case CacheKeyTempData7_1:
		return "TempData7_1"
	case CacheKeyTempData9:
		return "TempData9"
	case CacheKeyLastMsg:
		return "LastMsg"
	default:
		return "Unknown"
	}
}
