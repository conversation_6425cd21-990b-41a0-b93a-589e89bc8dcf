package fsm

import (
	"fmt"
	"remnawave-tg-shop-bot/internal/cache"
)

// State представляет состояние машины состояний
type State int

// Определение всех состояний FSM
const (
	// Базовое состояние
	StateIdle State = 0

	// Состояния для рассылок
	StateBroadcastWaitText    State = 1
	StateBroadcastCreateText  State = 1001
	StateBroadcastCreateTime  State = 1002
	StateBroadcastCreateReady State = 1003

	// Состояния для мгновенной рассылки
	StateBroadcastInstantText         State = 10
	StateBroadcastInstantConfirm      State = 11
	StateBroadcastInstantTargetSelect State = 12

	// Состояния для редактирования рассылок
	StateBroadcastEditText  State = 3001
	StateBroadcastEditTime  State = 3002
	StateBroadcastEditReady State = 3003

	// Состояния для тарифов
	StateTariffCode           State = 4001
	StateTariffTitle          State = 4002
	StateTariffPriceRUB       State = 4003
	StateTariffPriceStars     State = 4004
	StateTariffConfirm        State = 4006
	StateTariffEditTitle      State = 4007
	StateTariffEditPriceRUB   State = 4008
	StateTariffEditPriceStars State = 4009
	StateTariffEditConfirm    State = 4010
)

// String возвращает строковое представление состояния
func (s State) String() string {
	switch s {
	case StateIdle:
		return "Idle"
	case StateBroadcastWaitText:
		return "BroadcastWaitText"
	case StateBroadcastCreateText:
		return "BroadcastCreateText"
	case StateBroadcastCreateTime:
		return "BroadcastCreateTime"
	case StateBroadcastCreateReady:
		return "BroadcastCreateReady"
	case StateBroadcastInstantText:
		return "BroadcastInstantText"
	case StateBroadcastInstantConfirm:
		return "BroadcastInstantConfirm"
	case StateBroadcastInstantTargetSelect:
		return "BroadcastInstantTargetSelect"
	case StateBroadcastEditText:
		return "BroadcastEditText"
	case StateBroadcastEditTime:
		return "BroadcastEditTime"
	case StateBroadcastEditReady:
		return "BroadcastEditReady"
	case StateTariffCode:
		return "TariffCode"
	case StateTariffTitle:
		return "TariffTitle"
	case StateTariffPriceRUB:
		return "TariffPriceRUB"
	case StateTariffPriceStars:
		return "TariffPriceStars"
	case StateTariffConfirm:
		return "TariffConfirm"
	case StateTariffEditTitle:
		return "TariffEditTitle"
	case StateTariffEditPriceRUB:
		return "TariffEditPriceRUB"
	case StateTariffEditPriceStars:
		return "TariffEditPriceStars"
	case StateTariffEditConfirm:
		return "TariffEditConfirm"
	default:
		return fmt.Sprintf("Unknown(%d)", int(s))
	}
}

// IsBroadcastState проверяет, относится ли состояние к рассылкам
func (s State) IsBroadcastState() bool {
	switch s {
	case StateBroadcastWaitText, StateBroadcastCreateText, StateBroadcastCreateTime, StateBroadcastCreateReady,
		StateBroadcastInstantText, StateBroadcastInstantConfirm, StateBroadcastInstantTargetSelect,
		StateBroadcastEditText, StateBroadcastEditTime, StateBroadcastEditReady:
		return true
	default:
		return false
	}
}

// IsTariffState проверяет, относится ли состояние к тарифам
func (s State) IsTariffState() bool {
	switch s {
	case StateTariffCode, StateTariffTitle, StateTariffPriceRUB, StateTariffPriceStars, StateTariffConfirm,
		StateTariffEditTitle, StateTariffEditPriceRUB, StateTariffEditPriceStars, StateTariffEditConfirm:
		return true
	default:
		return false
	}
}

// IsTerminalState проверяет, является ли состояние терминальным
func (s State) IsTerminalState() bool {
	switch s {
	case StateBroadcastCreateReady, StateBroadcastEditReady, StateTariffConfirm, StateTariffEditConfirm:
		return true
	default:
		return false
	}
}

// FSMManager управляет состояниями машины состояний
type FSMManager struct {
	cache *cache.Cache
}

// NewFSMManager создает новый менеджер FSM
func NewFSMManager(cache *cache.Cache) *FSMManager {
	return &FSMManager{
		cache: cache,
	}
}

// GetState получает текущее состояние пользователя
func (f *FSMManager) GetState(userID int64) (State, bool) {
	state, ok := f.cache.GetInt(userID)
	if !ok {
		return StateIdle, false
	}
	return State(state), true
}

// SetState устанавливает состояние пользователя
func (f *FSMManager) SetState(userID int64, state State) error {
	f.cache.SetInt(userID, int(state))
	return nil
}

// ClearState очищает состояние пользователя (устанавливает в Idle)
func (f *FSMManager) ClearState(userID int64) error {
	f.cache.SetInt(userID, int(StateIdle))
	return nil
}

// ValidateTransition проверяет, допустим ли переход между состояниями
func (f *FSMManager) ValidateTransition(from, to State) bool {
	// Из любого состояния можно перейти в Idle
	if to == StateIdle {
		return true
	}

	// Логика валидации переходов для рассылок
	if from.IsBroadcastState() && to.IsBroadcastState() {
		return f.validateBroadcastTransition(from, to)
	}

	// Логика валидации переходов для тарифов
	if from.IsTariffState() && to.IsTariffState() {
		return f.validateTariffTransition(from, to)
	}

	// Переходы между разными типами состояний через Idle
	if from != StateIdle && to != StateIdle && from.IsBroadcastState() != to.IsBroadcastState() {
		return false
	}

	return true
}

// validateBroadcastTransition проверяет переходы для состояний рассылок
func (f *FSMManager) validateBroadcastTransition(from, to State) bool {
	validTransitions := map[State][]State{
		StateBroadcastWaitText: {StateBroadcastCreateText, StateBroadcastInstantText},
		StateBroadcastCreateText: {StateBroadcastCreateTime},
		StateBroadcastCreateTime: {StateBroadcastCreateReady},
		StateBroadcastInstantText: {StateBroadcastInstantConfirm},
		StateBroadcastInstantConfirm: {StateBroadcastInstantTargetSelect},
		StateBroadcastEditText: {StateBroadcastEditTime},
		StateBroadcastEditTime: {StateBroadcastEditReady},
	}

	allowedStates, exists := validTransitions[from]
	if !exists {
		return false
	}

	for _, allowed := range allowedStates {
		if to == allowed {
			return true
		}
	}
	return false
}

// validateTariffTransition проверяет переходы для состояний тарифов
func (f *FSMManager) validateTariffTransition(from, to State) bool {
	validTransitions := map[State][]State{
		StateTariffCode: {StateTariffTitle},
		StateTariffTitle: {StateTariffPriceRUB},
		StateTariffPriceRUB: {StateTariffPriceStars},
		StateTariffPriceStars: {StateTariffConfirm},
		StateTariffEditTitle: {StateTariffEditPriceRUB},
		StateTariffEditPriceRUB: {StateTariffEditPriceStars},
		StateTariffEditPriceStars: {StateTariffEditConfirm},
	}

	allowedStates, exists := validTransitions[from]
	if !exists {
		return false
	}

	for _, allowed := range allowedStates {
		if to == allowed {
			return true
		}
	}
	return false
}

// SetStateWithValidation устанавливает состояние с проверкой валидности перехода
func (f *FSMManager) SetStateWithValidation(userID int64, newState State) error {
	currentState, _ := f.GetState(userID)
	
	if !f.ValidateTransition(currentState, newState) {
		return fmt.Errorf("недопустимый переход из состояния %s в %s", currentState, newState)
	}
	
	return f.SetState(userID, newState)
}
