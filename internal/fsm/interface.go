package fsm

// FSM определяет интерфейс для работы с машиной состояний
type FSM interface {
	// GetState получает текущее состояние пользователя
	GetState(userID int64) (State, bool)
	
	// SetState устанавливает состояние пользователя
	SetState(userID int64, state State) error
	
	// ClearState очищает состояние пользователя (устанавливает в Idle)
	ClearState(userID int64) error
	
	// ValidateTransition проверяет, допустим ли переход между состояниями
	ValidateTransition(from, to State) bool
	
	// SetStateWithValidation устанавливает состояние с проверкой валидности перехода
	SetStateWithValidation(userID int64, newState State) error
}

// Убеждаемся, что FSMManager реализует интерфейс FSM
var _ FSM = (*FSMManager)(nil)
