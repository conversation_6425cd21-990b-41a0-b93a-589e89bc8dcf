package fsm

// MigrationHelper содержит мапы для замены старых констант на новые
var OldToNewStateMap = map[string]string{
	// Рассылки
	"adminBroadcastStateWaitText":    "fsm.StateBroadcastWaitText",
	"adminBroadcastStateCreateText":  "fsm.StateBroadcastCreateText",
	"adminBroadcastStateCreateTime":  "fsm.StateBroadcastCreateTime",
	"adminBroadcastStateCreateReady": "fsm.StateBroadcastCreateReady",
	
	// Мгновенные рассылки
	"adminBroadcastStateInstantText":         "fsm.StateBroadcastInstantText",
	"adminBroadcastStateInstantConfirm":      "fsm.StateBroadcastInstantConfirm",
	"adminBroadcastStateInstantTargetSelect": "fsm.StateBroadcastInstantTargetSelect",
	
	// Редактирование рассылок
	"adminBroadcastStateEditText":  "fsm.StateBroadcastEditText",
	"adminBroadcastStateEditTime":  "fsm.StateBroadcastEditTime",
	"adminBroadcastStateEditReady": "fsm.StateBroadcastEditReady",
	
	// Тарифы
	"tariffFSMStateCode":           "fsm.StateTariffCode",
	"tariffFSMStateTitle":          "fsm.StateTariffTitle",
	"tariffFSMStatePriceRUB":       "fsm.StateTariffPriceRUB",
	"tariffFSMStatePriceStars":     "fsm.StateTariffPriceStars",
	"tariffFSMStateConfirm":        "fsm.StateTariffConfirm",
	"tariffFSMStateEditTitle":      "fsm.StateTariffEditTitle",
	"tariffFSMStateEditPriceRUB":   "fsm.StateTariffEditPriceRUB",
	"tariffFSMStateEditPriceStars": "fsm.StateTariffEditPriceStars",
	"tariffFSMStateEditConfirm":    "fsm.StateTariffEditConfirm",
}

// GetNewStateConstant возвращает новую константу для старой
func GetNewStateConstant(oldConstant string) (string, bool) {
	newConstant, exists := OldToNewStateMap[oldConstant]
	return newConstant, exists
}

// GetStateValue возвращает значение состояния по старой константе
func GetStateValue(oldConstant string) (State, bool) {
	switch oldConstant {
	case "adminBroadcastStateWaitText":
		return StateBroadcastWaitText, true
	case "adminBroadcastStateCreateText":
		return StateBroadcastCreateText, true
	case "adminBroadcastStateCreateTime":
		return StateBroadcastCreateTime, true
	case "adminBroadcastStateCreateReady":
		return StateBroadcastCreateReady, true
	case "adminBroadcastStateInstantText":
		return StateBroadcastInstantText, true
	case "adminBroadcastStateInstantConfirm":
		return StateBroadcastInstantConfirm, true
	case "adminBroadcastStateInstantTargetSelect":
		return StateBroadcastInstantTargetSelect, true
	case "adminBroadcastStateEditText":
		return StateBroadcastEditText, true
	case "adminBroadcastStateEditTime":
		return StateBroadcastEditTime, true
	case "adminBroadcastStateEditReady":
		return StateBroadcastEditReady, true
	case "tariffFSMStateCode":
		return StateTariffCode, true
	case "tariffFSMStateTitle":
		return StateTariffTitle, true
	case "tariffFSMStatePriceRUB":
		return StateTariffPriceRUB, true
	case "tariffFSMStatePriceStars":
		return StateTariffPriceStars, true
	case "tariffFSMStateConfirm":
		return StateTariffConfirm, true
	case "tariffFSMStateEditTitle":
		return StateTariffEditTitle, true
	case "tariffFSMStateEditPriceRUB":
		return StateTariffEditPriceRUB, true
	case "tariffFSMStateEditPriceStars":
		return StateTariffEditPriceStars, true
	case "tariffFSMStateEditConfirm":
		return StateTariffEditConfirm, true
	default:
		return StateIdle, false
	}
}
