# Этап 1. Аналитика и подготовка - Детальный чек-лист

## 1.1. Аудит всех функций и структур handler.go

### 1.1.1. Список всех экспортируемых и внутренних функций

#### Экспортируемые функции (публичные):
- `NewHandler()` - конструктор Handler
- `AdminCommandHandler()` - обработчик команды /admin
- `AdminCallbackHandler()` - обработчик callback-ов из админ-меню
- `AdminTextHandler()` - универсальный обработчик текстовых сообщений от администратора
- `StartCommandHandler()` - обработчик команды /start
- `StartCallbackHandler()` - обработчик callback для start
- `ConnectCommandHandler()` - обработчик команды /connect
- `ConnectCallbackHandler()` - обработчик callback для connect
- `BuyCallbackHandler()` - обработчик покупки
- `SellCallbackHandler()` - обработчик продажи
- `PaymentCallbackHandler()` - обработчик платежей
- `SuccessPaymentHandler()` - обработчик успешных платежей
- `PreCheckoutCallbackHandler()` - обработчик предварительной проверки
- `ReferralCallbackHandler()` - обработчик реферальной системы
- `TrialCallbackHandler()` - обработчик пробного периода
- `ActivateTrialCallbackHandler()` - обработчик активации пробного периода
- `SyncUsersCommandHandler()` - обработчик синхронизации пользователей
- `CreateCustomerIfNotExistMiddleware()` - middleware для создания клиентов

#### Внутренние функции (приватные):
- `FormatTimeWithTZ()` - форматирование времени с часовым поясом
- `abs()` - функция модуля числа
- `buildAdminMenu()` - генерация главного админ-меню
- `buildBroadcastMenu()` - генерация подменю рассылок
- `buildStartKeyboard()` - генерация клавиатуры для start
- `resolveConnectButton()` - создание кнопки подключения
- `createConnectKeyboard()` - создание клавиатуры подключения
- `sendPaginatedBroadcastList()` - отправка пагинированного списка рассылок
- `showTariffsPage()` - показ страницы тарифов
- `clearBroadcastMessages()` - очистка сообщений рассылок
- `clearTariffMessages()` - очистка сообщений тарифов
- Множество других вспомогательных функций для генерации UI

### 1.1.2. Определение зон ответственности

#### Зона 1: FSM (Finite State Machine) - Машины состояний
**Константы состояний:**
- `adminBroadcastStateInstantText = 100`
- `adminBroadcastStateCreateText = 200`
- `adminBroadcastStateCreateTime = 201`
- `adminBroadcastStateCreateReady = 202`
- `tariffFSMStateCode = 300`
- `tariffFSMStateTitle = 301`
- `tariffFSMStatePriceRUB = 302`
- `tariffFSMStatePriceStars = 303`
- `tariffFSMStateConfirm = 304`
- И другие состояния для редактирования тарифов

**Функции FSM:**
- Логика переходов между состояниями в `AdminCallbackHandler()`
- Обработка текстовых сообщений в зависимости от состояния в `AdminTextHandler()`
- Управление состояниями через кеш

#### Зона 2: Генерация UI (клавиатуры и сообщения)
**Функции генерации клавиатур:**
- `buildAdminMenu()` - админ-меню
- `buildBroadcastMenu()` - меню рассылок
- `buildInstantBroadcastMenu()` - меню моментальной рассылки
- `buildCreateBroadcastMenu()` - меню создания рассылки
- `buildTariffListPaginationMenu()` - пагинация тарифов
- `buildSingleTariffEntry()` - отдельная запись тарифа
- `buildSingleBroadcastEntry()` - отдельная запись рассылки
- `buildStartKeyboard()` - стартовая клавиатура
- `createConnectKeyboard()` - клавиатура подключения

#### Зона 3: Бизнес-логика рассылок
**Функции рассылок:**
- Создание рассылок в `AdminCallbackHandler()`
- Обработка моментальных рассылок
- Управление отложенными рассылками
- Пагинация списка рассылок в `sendPaginatedBroadcastList()`
- Очистка сообщений рассылок в `clearBroadcastMessages()`

#### Зона 4: Бизнес-логика тарифов
**Функции тарифов:**
- Создание тарифов через FSM
- Редактирование тарифов
- Отображение списка тарифов в `showTariffsPage()`
- Пагинация тарифов
- Очистка сообщений тарифов в `clearTariffMessages()`

#### Зона 5: Работа с кешем
**Паттерны кеша:**
- Сохранение состояний FSM: `h.cache.SetInt(userID, state)`
- Сохранение временных данных: `h.cache.SetString(offset+userID, data)`
- Сохранение ID сообщений для удаления: `h.cache.SetInt(9999999+userID, msgID)`
- Очистка кеша: `h.cache.Delete(key)`

#### Зона 6: Обработка платежей
**Функции платежей:**
- `BuyCallbackHandler()` - выбор тарифа
- `SellCallbackHandler()` - выбор способа оплаты
- `PaymentCallbackHandler()` - создание платежа
- `SuccessPaymentHandler()` - обработка успешного платежа
- `PreCheckoutCallbackHandler()` - предварительная проверка

#### Зона 7: Пользовательские команды
**Основные команды:**
- `StartCommandHandler()` / `StartCallbackHandler()` - стартовое меню
- `ConnectCommandHandler()` / `ConnectCallbackHandler()` - подключение
- `ReferralCallbackHandler()` - реферальная система
- `TrialCallbackHandler()` / `ActivateTrialCallbackHandler()` - пробный период

#### Зона 8: Административные функции
**Админ-функции:**
- `AdminCommandHandler()` - главное админ-меню
- `AdminCallbackHandler()` - обработка админ-действий
- `AdminTextHandler()` - обработка админ-текста
- `SyncUsersCommandHandler()` - синхронизация

#### Зона 9: Middleware и утилиты
**Middleware:**
- `CreateCustomerIfNotExistMiddleware()` - создание клиентов

**Утилиты:**
- `FormatTimeWithTZ()` - форматирование времени
- `abs()` - математические функции
- Функции парсинга callback-данных

## 1.2. Точки входа (экспортируемые функции)

### 1.2.1. Места использования handler.go в проекте
Из анализа `cmd/app/main.go`:

```go
// Регистрация обработчиков команд
b.RegisterHandler(bot.HandlerTypeMessageText, "/start", bot.MatchTypePrefix, h.StartCommandHandler)
b.RegisterHandler(bot.HandlerTypeMessageText, "/connect", bot.MatchTypeExact, h.ConnectCommandHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeMessageText, "/sync", bot.MatchTypeExact, h.SyncUsersCommandHandler, isAdminMiddleware)
b.RegisterHandler(bot.HandlerTypeMessageText, "/admin", bot.MatchTypeExact, h.AdminCommandHandler, isAdminMiddleware)

// Регистрация callback-обработчиков
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackStart, bot.MatchTypeExact, h.StartCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackConnect, bot.MatchTypeExact, h.ConnectCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackBuy, bot.MatchTypeExact, h.BuyCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
// ... и другие callback-обработчики

// Обработчики платежей
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "", bot.MatchTypeContains, h.AdminCallbackHandler, isAdminMiddleware)
b.RegisterHandlerMatchFunc(func(update *models.Update) bool {
    return update.Message != nil && update.Message.From != nil && update.Message.From.ID == config.GetAdminTelegramId()
}, h.AdminTextHandler)
```

### 1.2.2. Публичные функции, вызываемые извне
- Все обработчики команд и callback-ов
- Middleware функции
- Конструктор `NewHandler()`

## 1.3. Зависимости handler.go

### 1.3.1. Схема зависимостей

```
Handler
├── Repositories
│   ├── customerRepository (*database.CustomerRepository)
│   ├── purchaseRepository (*database.PurchaseRepository)
│   ├── referralRepository (*database.ReferralRepository)
│   └── tariffRepository (*database.TariffRepository)
├── External Clients
│   ├── cryptoPayClient (*cryptopay.Client)
│   └── yookasaClient (*yookasa.Client)
├── Services
│   ├── translation (*translation.Manager)
│   ├── paymentService (*payment.PaymentService)
│   ├── syncService (*sync.SyncService)
│   └── broadcastTaskService (*service.BroadcastTaskService)
├── Infrastructure
│   ├── cache (*cache.Cache)
│   └── BroadcastWakeup (chan struct{})
└── External APIs
    └── Telegram Bot API (через параметр *bot.Bot)
```

### 1.3.2. Структуры, передаваемые в Handler

```go
type Handler struct {
    customerRepository   *database.CustomerRepository
    purchaseRepository   *database.PurchaseRepository
    cryptoPayClient      *cryptopay.Client
    yookasaClient        *yookasa.Client
    translation          *translation.Manager
    paymentService       *payment.PaymentService
    syncService          *sync.SyncService
    referralRepository   *database.ReferralRepository
    cache                *cache.Cache
    broadcastTaskService *service.BroadcastTaskService
    BroadcastWakeup      chan struct{}
    tariffRepository     *database.TariffRepository
}
```

## 1.4. Повторяющиеся паттерны

### 1.4.1. Найденные повторяющиеся блоки кода

#### Паттерн 1: Логирование ошибок
```go
if err != nil {
    slog.Error("Описание ошибки", err)
    return
}
```
**Встречается:** Во всех обработчиках, ~50+ раз

#### Паттерн 2: Работа с кешем состояний FSM
```go
state, ok := h.cache.GetInt(userID)
if !ok {
    // обработка отсутствия состояния
}
h.cache.SetInt(userID, newState)
```
**Встречается:** В админ-обработчиках, ~20+ раз

#### Паттерн 3: Очистка кеша
```go
h.cache.SetInt(callback.From.ID, 0)
h.cache.SetString(1000000+callback.From.ID, "")
h.cache.SetString(2000000+callback.From.ID, "")
// ... множественные очистки
```
**Встречается:** При переходах между меню, ~10+ раз

#### Паттерн 4: Отправка/редактирование сообщений
```go
msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
    ChatID:    chatID,
    Text:      text,
    ParseMode: models.ParseModeHTML,
    ReplyMarkup: models.InlineKeyboardMarkup{
        InlineKeyboard: keyboard,
    },
})
if err != nil {
    slog.Error("Ошибка отправки сообщения", err)
} else {
    h.cache.SetInt(9999999+userID, msg.ID)
}
```
**Встречается:** Во всех UI-функциях, ~30+ раз

#### Паттерн 5: Удаление старых сообщений
```go
if msgIDs, ok := h.cache.Get(offset + userID); ok {
    for _, msgID := range msgIDs.([]int) {
        b.DeleteMessage(ctx, &bot.DeleteMessageParams{
            ChatID:    chatID,
            MessageID: msgID,
        })
    }
    h.cache.Delete(offset + userID)
}
```
**Встречается:** В функциях очистки, ~5+ раз

#### Паттерн 6: Проверка прав администратора
```go
if update.Message.From.ID != config.GetAdminTelegramId() {
    slog.Warn("Not admin", "user_id", update.Message.From.ID)
    return
}
```
**Встречается:** В админ-функциях, ~5+ раз

#### Паттерн 7: Извлечение пользователя из базы
```go
customer, err := h.customerRepository.FindByTelegramId(ctx, userID)
if err != nil {
    slog.Error("Error finding customer", err)
    return
}
if customer == nil {
    slog.Error("customer not exist", "telegramId", userID)
    return
}
```
**Встречается:** Во многих обработчиках, ~15+ раз

#### Паттерн 8: Генерация клавиатур
```go
func buildXXXMenu() (string, [][]models.InlineKeyboardButton) {
    return "Текст меню", [][]models.InlineKeyboardButton{
        {{Text: "Кнопка", CallbackData: "callback"}},
    }
}
```
**Встречается:** Множество функций генерации UI, ~15+ функций

### 1.4.2. Что можно вынести в утилиты/хелперы

#### Группа 1: Утилиты для работы с сообщениями
- Отправка сообщений с стандартной обработкой ошибок
- Редактирование сообщений
- Удаление сообщений
- Сохранение ID сообщений в кеш

#### Группа 2: Утилиты для работы с кешем
- Управление состояниями FSM
- Очистка кеша по паттернам
- Сохранение/извлечение временных данных

#### Группа 3: Утилиты для работы с пользователями
- Извлечение пользователя с проверками
- Проверка прав администратора
- Извлечение языкового кода

#### Группа 4: Утилиты логирования
- Стандартизированное логирование ошибок
- Логирование действий пользователей

#### Группа 5: Генераторы UI
- Базовые функции для создания клавиатур
- Утилиты пагинации
- Стандартные кнопки (Назад, Отмена и т.д.)

## Заключение Этапа 1.1

**Общая статистика:**
- Экспортируемых функций: ~18
- Внутренних функций: ~25+
- Основных зон ответственности: 9
- Повторяющихся паттернов: 8 основных групп
- Зависимостей: 12 основных

**Готовность к рефакторингу:**
✅ Аудит завершён
✅ Зоны ответственности определены
✅ Повторяющиеся паттерны выявлены
✅ Зависимости проанализированы

**Следующий шаг:** Переход к пункту 1.2 - фиксация точек входа и использования