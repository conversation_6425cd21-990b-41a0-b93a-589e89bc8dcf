# Этап 1.2. Фиксация текущих точек входа

## 1.2.1. Все места использования handler.go в проекте

### Основное использование в cmd/app/main.go:

#### Создание Handler:
```go
h := handler.NewHandler(
    syncService, paymentService, tm, customerRepository, 
    purchaseRepository, cryptoPayClient, yookasaClient, 
    referralRepository, cache, broadcastTaskService, 
    broadcastWakeup, tariffRepository
)
```

#### Регистрация обработчиков команд:
```go
// Пользовательские команды
b.RegisterHandler(bot.HandlerTypeMessageText, "/start", bot.MatchTypePrefix, h.<PERSON><PERSON><PERSON><PERSON>andler)
b.<PERSON><PERSON><PERSON><PERSON>(bot.HandlerTypeMessageText, "/connect", bot.MatchTypeExact, h.<PERSON>, h.<PERSON><PERSON><PERSON>IfNotExistMiddleware)

// Административные команды
b.RegisterHandler(bot.HandlerTypeMessageText, "/sync", bot.MatchTypeExact, h.SyncUsersCommandHandler, isAdminMiddleware)
b.RegisterHandler(bot.HandlerTypeMessageText, "/admin", bot.MatchTypeExact, h.AdminCommandHandler, isAdminMiddleware)
```

#### Регистрация callback-обработчиков:
```go
// Основные callback-и
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackStart, bot.MatchTypeExact, h.StartCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackConnect, bot.MatchTypeExact, h.ConnectCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackBuy, bot.MatchTypeExact, h.BuyCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackSell, bot.MatchTypePrefix, h.SellCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackPayment, bot.MatchTypePrefix, h.PaymentCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackTrial, bot.MatchTypeExact, h.TrialCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackActivateTrial, bot.MatchTypeExact, h.ActivateTrialCallbackHandler, h.CreateCustomerIfNotExistMiddleware)
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, CallbackReferral, bot.MatchTypeExact, h.ReferralCallbackHandler, h.CreateCustomerIfNotExistMiddleware)

// Административные callback-и
b.RegisterHandler(bot.HandlerTypeCallbackQueryData, "", bot.MatchTypeContains, h.AdminCallbackHandler, isAdminMiddleware)
```

#### Специальные обработчики:
```go
// Обработчики платежей
b.RegisterHandler(bot.HandlerTypePreCheckoutQuery, "", bot.MatchTypeContains, h.PreCheckoutCallbackHandler, h.CreateCustomerIfNotExistMiddleware)

b.RegisterHandlerMatchFunc(func(update *models.Update) bool {
    return update.Message != nil && update.Message.SuccessfulPayment != nil
}, h.SuccessPaymentHandler)

// Обработчик текстовых сообщений от админа
b.RegisterHandlerMatchFunc(func(update *models.Update) bool {
    return update.Message != nil && update.Message.From != nil && update.Message.From.ID == config.GetAdminTelegramId()
}, h.AdminTextHandler)
```

### Использование в других файлах:

#### В internal/tribute/tribute.go:
```go
// Косвенное использование через paymentService, который использует Handler
err = c.paymentService.ProcessPurchaseById(ctx, purchaseId)
```

## 1.2.2. Описание функций, вызываемых снаружи

### Группа 1: Конструктор
- **NewHandler()** - создание экземпляра Handler с внедрением всех зависимостей

### Группа 2: Команды пользователей
- **StartCommandHandler()** - обработка команды /start, создание стартового меню
- **ConnectCommandHandler()** - обработка команды /connect, показ информации о подключении

### Группа 3: Административные команды  
- **AdminCommandHandler()** - обработка команды /admin, создание админ-меню
- **SyncUsersCommandHandler()** - обработка команды /sync, синхронизация пользователей

### Группа 4: Callback-обработчики пользователей
- **StartCallbackHandler()** - возврат в главное меню
- **ConnectCallbackHandler()** - показ информации о подключении через callback
- **BuyCallbackHandler()** - выбор тарифа для покупки
- **SellCallbackHandler()** - выбор способа оплаты
- **PaymentCallbackHandler()** - создание платежа
- **TrialCallbackHandler()** - активация пробного периода
- **ActivateTrialCallbackHandler()** - подтверждение активации пробного периода
- **ReferralCallbackHandler()** - работа с реферальной системой

### Группа 5: Административные callback-обработчики
- **AdminCallbackHandler()** - универсальный обработчик всех админ-действий
- **AdminTextHandler()** - обработка текстовых сообщений от администратора

### Группа 6: Обработчики платежей
- **PreCheckoutCallbackHandler()** - предварительная проверка платежа
- **SuccessPaymentHandler()** - обработка успешного платежа

### Группа 7: Middleware
- **CreateCustomerIfNotExistMiddleware()** - создание клиента, если он не существует

## 1.2.3. Критические точки интеграции

### Точка 1: Telegram Bot API
- Все обработчики получают параметры `(ctx context.Context, b *bot.Bot, update *models.Update)`
- Используют методы `b.SendMessage()`, `b.EditMessageText()`, `b.DeleteMessage()` и др.

### Точка 2: Middleware Chain
- Многие обработчики используют `h.CreateCustomerIfNotExistMiddleware`
- Админ-функции используют `isAdminMiddleware` (определён в main.go)

### Точка 3: FSM State Management
- Состояния сохраняются в кеше: `h.cache.SetInt(userID, state)`
- Состояния читаются: `state, ok := h.cache.GetInt(userID)`

### Точка 4: Cross-Handler Communication
- Обработчики взаимодействуют через кеш
- Используют каналы: `BroadcastWakeup chan struct{}`

## 1.2.4. Контракты и зависимости

### Входные контракты:
```go
// Все обработчики команд и callback-ов
func (h Handler) XxxHandler(ctx context.Context, b *bot.Bot, update *models.Update)

// Middleware
func (h Handler) CreateCustomerIfNotExistMiddleware(next bot.HandlerFunc) bot.HandlerFunc

// Конструктор
func NewHandler(...12 параметров...) *Handler
```

### Выходные контракты:
- Отправка сообщений в Telegram
- Изменение состояния в кеше
- Вызовы методов сервисов и репозиториев
- Логирование через slog

### Критические зависимости:
1. **Telegram Bot API** - нельзя изменить сигнатуры обработчиков
2. **Cache Interface** - используется для FSM и временных данных
3. **Repository Interfaces** - доступ к данным
4. **Service Interfaces** - бизнес-логика
5. **Translation Manager** - локализация
6. **Config Package** - конфигурация

## Заключение Этапа 1.2

**Зафиксированные точки входа:**
- ✅ 18 публичных обработчиков
- ✅ 1 middleware функция  
- ✅ 1 конструктор
- ✅ Все регистрации в main.go задокументированы

**Критические ограничения для рефакторинга:**
- 🔒 Нельзя менять сигнатуры обработчиков (Telegram Bot API)
- 🔒 Нельзя менять поведение middleware
- 🔒 Нельзя нарушать FSM логику
- 🔒 Нельзя менять структуру кеша без миграции

**Готовность к следующему этапу:** ✅