# Этап 1.3. Описание зависимостей handler.go

## 1.3.1. Схема зависимостей между handler.go и другими пакетами

```mermaid
graph TD
    H[Handler] --> CR[CustomerRepository]
    H --> PR[PurchaseRepository] 
    H --> RR[ReferralRepository]
    H --> TR[TariffRepository]
    H --> CPC[CryptoPayClient]
    H --> YC[YookasaClient]
    H --> TM[Translation Manager]
    H --> PS[PaymentService]
    H --> SS[SyncService]
    H --> BTS[BroadcastTaskService]
    H --> C[Cache]
    H --> BW[BroadcastWakeup Channel]
    
    CR --> DB[(Database)]
    PR --> DB
    RR --> DB
    TR --> DB
    
    CPC --> CAPI[CryptoPay API]
    YC --> YAPI[Yookasa API]
    
    PS --> CR
    PS --> PR
    PS --> RR
    PS --> RC[RemnaWave Client]
    PS --> TM
    PS --> CPC
    PS --> YC
    PS --> C
    
    SS --> RC
    SS --> CR
    
    BTS --> DB
    
    H --> TAPI[Telegram Bot API]
    H --> CFG[Config Package]
    H --> LOG[Slog Logger]
```

## 1.3.2. Детальный анализ всех структур, передаваемых в Handler

### Структура Handler:
```go
type Handler struct {
    // Репозитории данных
    customerRepository   *database.CustomerRepository   // Управление клиентами
    purchaseRepository   *database.PurchaseRepository   // Управление покупками  
    referralRepository   *database.ReferralRepository   // Реферальная система
    tariffRepository     *database.TariffRepository     // Управление тарифами
    
    // Внешние клиенты платёжных систем
    cryptoPayClient      *cryptopay.Client              // CryptoPay API
    yookasaClient        *yookasa.Client                // Yookasa API
    
    // Сервисы бизнес-логики
    translation          *translation.Manager           // Локализация
    paymentService       *payment.PaymentService        // Обработка платежей
    syncService          *sync.SyncService              // Синхронизация пользователей
    broadcastTaskService *service.BroadcastTaskService  // Управление рассылками
    
    // Инфраструктура
    cache                *cache.Cache                   // Кеширование и FSM
    BroadcastWakeup      chan struct{}                  // Канал уведомлений рассылок
}
```

### Анализ каждой зависимости:

#### 1. Репозитории (Data Layer)

**customerRepository (*database.CustomerRepository):**
- **Назначение:** CRUD операции с клиентами
- **Методы используемые в Handler:**
  - `FindByTelegramId(ctx, telegramId)` - поиск клиента
  - `Create(ctx, customer)` - создание клиента
  - `UpdateFields(ctx, id, updates)` - обновление полей
  - `GetAll(ctx)` - получение всех клиентов
  - `GetUsersWithoutActiveSubscription(ctx)` - клиенты без подписки
- **Частота использования:** Очень высокая (~15 вызовов)

**purchaseRepository (*database.PurchaseRepository):**
- **Назначение:** Управление покупками
- **Методы используемые в Handler:** Косвенно через paymentService
- **Частота использования:** Низкая (через сервисы)

**referralRepository (*database.ReferralRepository):**
- **Назначение:** Реферальная система
- **Методы используемые в Handler:**
  - `Create(ctx, referrerId, refereeId)` - создание реферала
  - `CountByReferrer(ctx, referrerId)` - подсчёт рефералов
- **Частота использования:** Средняя (~3 вызова)

**tariffRepository (*database.TariffRepository):**
- **Назначение:** Управление тарифами
- **Методы используемые в Handler:**
  - `GetAll(ctx, activeOnly)` - получение тарифов
  - `Create(ctx, tariff)` - создание тарифа
  - `Update(ctx, id, tariff)` - обновление тарифа
  - `Delete(ctx, id)` - удаление тарифа
  - `FindByCode(ctx, code)` - поиск по коду
- **Частота использования:** Высокая (~10 вызовов)

#### 2. Внешние клиенты (External APIs)

**cryptoPayClient (*cryptopay.Client):**
- **Назначение:** Интеграция с CryptoPay
- **Использование:** Косвенно через paymentService
- **Частота использования:** Низкая (через сервисы)

**yookasaClient (*yookasa.Client):**
- **Назначение:** Интеграция с Yookasa
- **Использование:** Косвенно через paymentService  
- **Частота использования:** Низкая (через сервисы)

#### 3. Сервисы бизнес-логики (Business Layer)

**translation (*translation.Manager):**
- **Назначение:** Локализация сообщений
- **Методы используемые в Handler:**
  - `GetText(langCode, key)` - получение переведённого текста
- **Частота использования:** Очень высокая (~30+ вызовов)

**paymentService (*payment.PaymentService):**
- **Назначение:** Обработка платежей
- **Методы используемые в Handler:**
  - `CreatePurchaseByTariff(ctx, tariff, customer, invoiceType)` - создание покупки
  - `ActivateTrial(ctx, telegramId)` - активация пробного периода
  - `ProcessPurchaseById(ctx, purchaseId)` - обработка покупки
- **Частота использования:** Средняя (~5 вызовов)

**syncService (*sync.SyncService):**
- **Назначение:** Синхронизация с внешними системами
- **Методы используемые в Handler:**
  - `Sync()` - запуск синхронизации
- **Частота использования:** Низкая (~1 вызов)

**broadcastTaskService (*service.BroadcastTaskService):**
- **Назначение:** Управление задачами рассылок
- **Методы используемые в Handler:**
  - `GetAll(ctx)` - получение всех задач
  - `Create(ctx, task)` - создание задачи
  - `Delete(ctx, id)` - удаление задачи
  - `Update(ctx, id, updates)` - обновление задачи
- **Частота использования:** Средняя (~8 вызовов)

#### 4. Инфраструктура (Infrastructure Layer)

**cache (*cache.Cache):**
- **Назначение:** Кеширование и управление состояниями FSM
- **Методы используемые в Handler:**
  - `SetInt(key, value)` - сохранение числа
  - `GetInt(key)` - получение числа
  - `SetString(key, value)` - сохранение строки
  - `GetString(key)` - получение строки
  - `Set(key, value)` - сохранение объекта
  - `Get(key)` - получение объекта
  - `Delete(key)` - удаление ключа
- **Частота использования:** Критически высокая (~50+ вызовов)

**BroadcastWakeup (chan struct{}):**
- **Назначение:** Канал для уведомления планировщика рассылок
- **Использование:** Отправка сигналов при создании рассылок
- **Частота использования:** Низкая (~2 вызова)

#### 5. Внешние зависимости (не в структуре Handler)

**Telegram Bot API (*bot.Bot):**
- **Источник:** Параметр всех обработчиков
- **Методы используемые:**
  - `SendMessage(ctx, params)` - отправка сообщения
  - `EditMessageText(ctx, params)` - редактирование текста
  - `EditMessageReplyMarkup(ctx, params)` - редактирование клавиатуры
  - `DeleteMessage(ctx, params)` - удаление сообщения
- **Частота использования:** Критически высокая (~40+ вызовов)

**Config Package:**
- **Методы используемые:**
  - `GetAdminTelegramId()` - ID администратора
  - `TrialDays()` - количество дней пробного периода
  - `IsCryptoPayEnabled()` - включён ли CryptoPay
  - `GetTributeWebHookUrl()` - URL webhook Tribute
  - `TimeZone()` - часовой пояс
- **Частота использования:** Средняя (~10 вызовов)

**Slog Logger:**
- **Методы используемые:**
  - `slog.Info()` - информационные сообщения
  - `slog.Error()` - ошибки
  - `slog.Warn()` - предупреждения
- **Частота использования:** Очень высокая (~60+ вызовов)

## 1.3.3. Анализ связанности и зависимостей

### Уровни связанности:

#### Критическая связанность (нельзя изменить):
- **Telegram Bot API** - определяет сигнатуры всех обработчиков
- **Cache** - хранит состояния FSM, критично для работы
- **Translation Manager** - используется во всех UI функциях

#### Высокая связанность (сложно изменить):
- **CustomerRepository** - используется в большинстве обработчиков
- **TariffRepository** - центральная часть админ-функций
- **Config Package** - определяет поведение системы

#### Средняя связанность (можно рефакторить):
- **BroadcastTaskService** - используется только в админ-функциях
- **PaymentService** - изолирован в платёжных обработчиках
- **ReferralRepository** - используется локально

#### Низкая связанность (легко изменить):
- **SyncService** - один метод, один вызов
- **CryptoPayClient/YookasaClient** - используются через сервисы
- **BroadcastWakeup** - простой канал

### Проблемы текущей архитектуры:

#### Проблема 1: Нарушение принципа единственной ответственности
- Handler делает слишком много: UI, FSM, бизнес-логику, работу с данными

#### Проблема 2: Высокая связанность
- 12 зависимостей в конструкторе
- Прямые вызовы репозиториев из обработчиков

#### Проблема 3: Отсутствие абстракций
- Прямая зависимость от конкретных типов
- Сложность тестирования

#### Проблема 4: Смешение уровней
- Обработчики содержат и UI логику, и бизнес-логику
- Прямая работа с кешем из обработчиков

## Заключение Этапа 1.3

**Проанализированные зависимости:**
- ✅ 12 основных зависимостей в Handler
- ✅ 3 внешние зависимости (Bot API, Config, Logger)
- ✅ Уровни связанности определены
- ✅ Проблемы архитектуры выявлены

**Рекомендации для рефакторинга:**
1. Вынести FSM логику в отдельный пакет
2. Создать слой сервисов для бизнес-логики
3. Абстрагировать работу с UI
4. Ввести интерфейсы для основных зависимостей
5. Уменьшить количество прямых зависимостей

**Готовность к следующему этапу:** ✅