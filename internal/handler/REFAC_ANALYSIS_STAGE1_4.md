# Этап 1.4. Составление списка повторяющихся паттернов

## 1.4.1. Детальный анализ повторяющихся блоков кода

### Паттерн 1: Обработка ошибок с логированием
**Код:**
```go
if err != nil {
    slog.Error("Описание ошибки", err)
    return
}
```

**Статистика:**
- Встречается: ~60 раз
- Файлы: handler.go (основной), middleware.go, sync.go, connect.go и др.
- Вариации: 
  - С дополнительными полями: `slog.Error("msg", "field", value, err)`
  - С разными уровнями: `slog.Warn()`, `slog.Info()`

**Проблемы:**
- Дублирование кода
- Непоследовательность в сообщениях об ошибках
- Отсутствие централизованной обработки

### Паттерн 2: Управление состояниями FSM
**Код:**
```go
// Получение состояния
state, ok := h.cache.GetInt(userID)
if !ok {
    slog.Warn("FSM state not found", "user_id", userID)
    return
}

// Установка состояния
h.cache.SetInt(userID, newState)

// Очистка состояния
h.cache.SetInt(userID, 0)
```

**Статистика:**
- Встречается: ~25 раз
- Локализация: AdminCallbackHandler, AdminTextHandler
- Константы состояний: ~15 различных состояний

**Проблемы:**
- Магические числа для состояний
- Отсутствие валидации переходов
- Сложность отладки FSM

### Паттерн 3: Массовая очистка кеша
**Код:**
```go
h.cache.SetInt(callback.From.ID, 0)
h.cache.SetString(1000000+callback.From.ID, "")
h.cache.SetString(2000000+callback.From.ID, "")
h.cache.SetString(3000000+callback.From.ID, "")
h.cache.SetString(4000000+callback.From.ID, "")
h.cache.Delete(7000000 + callback.From.ID)
h.cache.Delete(7000001 + callback.From.ID)
// ... ещё больше очисток
```

**Статистика:**
- Встречается: ~12 раз
- Магические смещения: 1000000, 2000000, 3000000, 4000000, 5000000, 7000000+, 9000000+, 9999999+
- Локализация: При переходах между меню

**Проблемы:**
- Магические числа для ключей кеша
- Риск забыть очистить какой-то ключ
- Сложность поддержки

### Паттерн 4: Отправка сообщений с сохранением ID
**Код:**
```go
msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
    ChatID:    chatID,
    Text:      text,
    ParseMode: models.ParseModeHTML,
    ReplyMarkup: models.InlineKeyboardMarkup{
        InlineKeyboard: keyboard,
    },
})
if err != nil {
    slog.Error("Ошибка отправки сообщения", err)
} else {
    h.cache.SetInt(9999999+userID, msg.ID)
}
```

**Статистика:**
- Встречается: ~35 раз
- Вариации: SendMessage, EditMessageText, EditMessageReplyMarkup
- Постоянное смещение: 9999999+ для сохранения ID сообщений

**Проблемы:**
- Дублирование логики отправки
- Магическое число 9999999
- Непоследовательность в обработке ошибок

### Паттерн 5: Удаление старых сообщений
**Код:**
```go
if msgIDs, ok := h.cache.Get(offset + userID); ok {
    for _, msgID := range msgIDs.([]int) {
        _, err := b.DeleteMessage(ctx, &bot.DeleteMessageParams{
            ChatID:    chatID,
            MessageID: msgID,
        })
        if err != nil {
            slog.Error("Ошибка удаления сообщения", err)
        }
    }
    h.cache.Delete(offset + userID)
}
```

**Статистика:**
- Встречается: ~8 раз
- Функции: clearBroadcastMessages, clearTariffMessages
- Смещения: 5000000, 5100000

**Проблемы:**
- Дублирование логики удаления
- Небезопасное приведение типов
- Магические смещения

### Паттерн 6: Извлечение пользователя из базы
**Код:**
```go
customer, err := h.customerRepository.FindByTelegramId(ctx, userID)
if err != nil {
    slog.Error("Error finding customer", err)
    return
}
if customer == nil {
    slog.Error("customer not exist", "telegramId", utils.MaskHalfInt64(userID))
    return
}
```

**Статистика:**
- Встречается: ~18 раз
- Файлы: handler.go, connect.go, trial.go, start.go, referral.go
- Вариации: с маскированием ID и без

**Проблемы:**
- Дублирование проверок
- Непоследовательность в логировании
- Отсутствие централизованной обработки

### Паттерн 7: Проверка прав администратора
**Код:**
```go
if update.Message.From.ID != config.GetAdminTelegramId() {
    slog.Warn("AdminCommandHandler: not admin", "user_id", update.Message.From.ID, "admin_id", config.GetAdminTelegramId())
    return
}
```

**Статистика:**
- Встречается: ~6 раз
- Локализация: Админ-обработчики
- Также есть middleware isAdminMiddleware в main.go

**Проблемы:**
- Дублирование проверок
- Смешение middleware и inline проверок
- Непоследовательность

### Паттерн 8: Генерация стандартных клавиатур
**Код:**
```go
func buildXXXMenu() (string, [][]models.InlineKeyboardButton) {
    return "Текст меню", [][]models.InlineKeyboardButton{
        {{Text: "Кнопка 1", CallbackData: "callback1"}},
        {{Text: "Кнопка 2", CallbackData: "callback2"}},
        {{Text: "⬅️ Назад", CallbackData: "back_callback"}},
    }
}
```

**Статистика:**
- Встречается: ~20 функций генерации
- Стандартные кнопки: "⬅️ Назад", "❌ Отмена", "✅ Подтвердить"
- Паттерны: Главное меню → Подменю → Действие

**Проблемы:**
- Дублирование структуры клавиатур
- Отсутствие переиспользования стандартных кнопок
- Сложность изменения стиля

### Паттерн 9: Пагинация списков
**Код:**
```go
func getXXXPage(items []Type, page int) ([]Type, int) {
    const itemsPerPage = 5
    totalPages := (len(items) + itemsPerPage - 1) / itemsPerPage
    if page < 1 {
        page = 1
    }
    if page > totalPages {
        page = totalPages
    }
    
    start := (page - 1) * itemsPerPage
    end := start + itemsPerPage
    if end > len(items) {
        end = len(items)
    }
    
    return items[start:end], totalPages
}
```

**Статистика:**
- Встречается: getBroadcastPage, getTariffPage
- Константа: itemsPerPage = 5
- Связанные функции: buildXXXPaginationMenu

**Проблемы:**
- Дублирование логики пагинации
- Жёстко заданный размер страницы
- Отсутствие переиспользования

### Паттерн 10: Парсинг callback данных
**Код:**
```go
// Простой парсинг
if strings.HasPrefix(callback.Data, "prefix_") {
    value := strings.TrimPrefix(callback.Data, "prefix_")
    // обработка
}

// Сложный парсинг с параметрами
callbackQuery := parseCallbackData(update.CallbackQuery.Data)
code := callbackQuery["code"]
invoiceType := callbackQuery["invoiceType"]
```

**Статистика:**
- Встречается: ~15 различных префиксов
- Функция parseCallbackData используется в payment_handlers.go
- Префиксы: "admin_", "tariff_", "broadcast_", "instant_broadcast", и др.

**Проблемы:**
- Непоследовательность в парсинге
- Отсутствие валидации данных
- Сложность отладки

## 1.4.2. Оценка возможности выноса в утилиты/хелперы

### Группа 1: Утилиты для работы с сообщениями (Высокий приоритет)
**Предлагаемый пакет:** `internal/handler/messaging`

**Функции:**
```go
// Отправка сообщения с автоматическим сохранением ID
func (m *MessagingHelper) SendMessage(ctx context.Context, b *bot.Bot, params *bot.SendMessageParams, userID int64) (*models.Message, error)

// Редактирование сообщения
func (m *MessagingHelper) EditMessage(ctx context.Context, b *bot.Bot, params *bot.EditMessageTextParams, userID int64) (*models.Message, error)

// Удаление группы сообщений
func (m *MessagingHelper) DeleteMessages(ctx context.Context, b *bot.Bot, chatID int64, userID int64, cacheOffset int64) error
```

**Выгода:** Устранение ~35 дублирований, централизованная обработка ошибок

### Группа 2: Утилиты для работы с FSM (Высокий приоритет)
**Предлагаемый пакет:** `internal/fsm`

**Функции:**
```go
type FSM interface {
    GetState(userID int64) (State, error)
    SetState(userID int64, state State) error
    ClearState(userID int64) error
    ValidateTransition(from, to State) bool
}

type State int
const (
    StateIdle State = iota
    StateBroadcastText
    StateBroadcastTime
    // ... другие состояния
)
```

**Выгода:** Устранение ~25 дублирований, типобезопасность, валидация переходов

### Группа 3: Утилиты для работы с кешем (Средний приоритет)
**Предлагаемый пакет:** `internal/handler/cache_helper`

**Функции:**
```go
type CacheHelper struct {
    cache *cache.Cache
}

// Очистка всех данных пользователя
func (c *CacheHelper) ClearUserData(userID int64) error

// Сохранение временных данных с типизированными ключами
func (c *CacheHelper) SetTempData(userID int64, dataType TempDataType, value interface{}) error
func (c *CacheHelper) GetTempData(userID int64, dataType TempDataType) (interface{}, bool)
```

**Выгода:** Устранение ~12 дублирований, типобезопасность ключей

### Группа 4: Утилиты для работы с пользователями (Средний приоритет)
**Предлагаемый пакет:** `internal/handler/user_helper`

**Функции:**
```go
type UserHelper struct {
    customerRepository *database.CustomerRepository
}

// Получение пользователя с проверками
func (u *UserHelper) GetCustomer(ctx context.Context, telegramID int64) (*database.Customer, error)

// Проверка прав администратора
func (u *UserHelper) IsAdmin(userID int64) bool

// Извлечение языкового кода
func (u *UserHelper) GetLanguageCode(update *models.Update) string
```

**Выгода:** Устранение ~18 дублирований, централизованная обработка

### Группа 5: Генераторы UI (Средний приоритет)
**Предлагаемый пакет:** `internal/handler/ui`

**Функции:**
```go
type UIBuilder struct {
    translation *translation.Manager
}

// Стандартные кнопки
func (u *UIBuilder) BackButton(langCode string, callback string) models.InlineKeyboardButton
func (u *UIBuilder) CancelButton(langCode string, callback string) models.InlineKeyboardButton
func (u *UIBuilder) ConfirmButton(langCode string, callback string) models.InlineKeyboardButton

// Пагинация
func (u *UIBuilder) BuildPagination(currentPage, totalPages int, callbackPrefix string) []models.InlineKeyboardButton
```

**Выгода:** Устранение ~20 дублирований, единообразие UI

### Группа 6: Утилиты пагинации (Низкий приоритет)
**Предлагаемый пакет:** `internal/handler/pagination`

**Функции:**
```go
type Paginator[T any] struct {
    ItemsPerPage int
}

func (p *Paginator[T]) GetPage(items []T, page int) ([]T, int)
func (p *Paginator[T]) BuildNavigation(currentPage, totalPages int, callbackPrefix string) []models.InlineKeyboardButton
```

**Выгода:** Устранение ~2 дублирований, типобезопасность

### Группа 7: Утилиты логирования (Низкий приоритет)
**Предлагаемый пакет:** `internal/handler/logging`

**Функции:**
```go
type Logger struct{}

func (l *Logger) LogError(operation string, err error, fields ...interface{})
func (l *Logger) LogUserAction(userID int64, action string, fields ...interface{})
func (l *Logger) LogAdminAction(userID int64, action string, fields ...interface{})
```

**Выгода:** Устранение ~60 дублирований, стандартизация логов

### Группа 8: Парсеры callback данных (Низкий приоритет)
**Предлагаемый пакет:** `internal/handler/callback`

**Функции:**
```go
type CallbackParser struct{}

func (c *CallbackParser) ParseCallback(data string) (action string, params map[string]string, error)
func (c *CallbackParser) BuildCallback(action string, params map[string]string) string
func (c *CallbackParser) HasPrefix(data string, prefix string) bool
```

**Выгода:** Устранение ~15 дублирований, валидация данных

## 1.4.3. Приоритизация рефакторинга

### Критический приоритет (Этап 2):
1. **FSM утилиты** - основа для всех админ-функций
2. **Messaging утилиты** - используются везде

### Высокий приоритет (Этап 3):
3. **User утилиты** - базовая функциональность
4. **Cache утилиты** - безопасность данных

### Средний приоритет (Этап 4):
5. **UI утилиты** - улучшение поддержки
6. **Pagination утилиты** - переиспользование

### Низкий приоритет (Этап 5):
7. **Logging утилиты** - стандартизация
8. **Callback утилиты** - удобство разработки

## Заключение Этапа 1.4

**Найденные паттерны:**
- ✅ 10 основных групп повторяющегося кода
- ✅ ~200+ дублирований в общей сложности
- ✅ 8 групп утилит для рефакторинга

**Потенциальная экономия:**
- Сокращение кода на ~40%
- Улучшение читаемости и поддержки
- Снижение количества ошибок

**Готовность к Этапу 2:** ✅

---

# ЗАКЛЮЧЕНИЕ ЭТАПА 1 - АНАЛИТИКА И ПОДГОТОВКА

## Выполненные задачи:
- ✅ 1.1. Аудит всех функций и структур handler.go
- ✅ 1.2. Фиксация текущих точек входа
- ✅ 1.3. Описание зависимостей handler.go  
- ✅ 1.4. Составление списка повторяющихся паттернов

## Ключевые выводы:

### Текущее состояние:
- **43 функции** в handler.go (18 экспортируемых, 25+ внутренних)
- **9 зон ответственности** (FSM, UI, рассылки, тарифы, платежи и др.)
- **12 основных зависимостей** в структуре Handler
- **10 групп повторяющихся паттернов** (~200+ дублирований)

### Критические проблемы:
1. **Нарушение SRP** - Handler делает слишком много
2. **Высокая связанность** - 12 зависимостей в конструкторе
3. **Дублирование кода** - ~200+ повторений
4. **Отсутствие абстракций** - прямые зависимости от конкретных типов
5. **Сложность тестирования** - монолитная структура

### План рефакторинга готов:
- **Этап 2:** Декомпозиция по зонам ответственности
- **Этап 3:** Внедрение интерфейсов и DI
- **Этап 4:** Унификация и оптимизация
- **Этап 5:** Документирование и поддержка

**СТАТУС: Этап 1 завершён ✅**
**СЛЕДУЮЩИЙ ШАГ: Переход к Этапу 2.1 - Вынос FSM-логики**