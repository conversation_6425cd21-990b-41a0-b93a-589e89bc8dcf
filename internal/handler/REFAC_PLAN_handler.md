# План полного рефакторинга handler.go

## Общие принципы
- Рефакторинг выполнять поэтапно, после каждого этапа — проверка работоспособности.
- Не допускать одновременного изменения нескольких зон ответственности.
- После каждого шага — запуск тестов и ручная проверка ключевых сценариев.
- Все изменения документировать и комментировать.
- Сохранять обратную совместимость: функционал должен работать как раньше.

---

## Этап 1. Аналитика и подготовка
- [ ] 1.1. Провести аудит всех функций и структур handler.go, составить карту ответственности:
    - [ ] 1.1.1. Составить список всех экспортируемых и внутренних функций.
    - [ ] 1.1.2. Определить, какие функции относятся к рассылкам, тарифам, FSM, генерации UI, кешу и т.д.
- [ ] 1.2. Зафиксировать текущие точки входа (экспортируемые функции, используемые извне):
    - [ ] 1.2.1. Найти все места использования handler.go в проекте.
    - [ ] 1.2.2. Описать, какие функции вызываются снаружи.
- [ ] 1.3. Описать зависимости handler.go (репозитории, сервисы, кеш, Telegram Bot API):
    - [ ] 1.3.1. Составить схему зависимостей между handler.go и другими пакетами.
    - [ ] 1.3.2. Зафиксировать все структуры, передаваемые в Handler.
- [ ] 1.4. Составить список повторяющихся паттернов (логирование, кеш, генерация клавиатур и т.д.):
    - [ ] 1.4.1. Найти и описать все повторяющиеся блоки кода.
    - [ ] 1.4.2. Оценить, что можно вынести в утилиты/хелперы.

---

## Этап 2. Декомпозиция по зонам ответственности
- [ ] 2.1. Вынести FSM-логику (машины состояний) в отдельный пакет (например, internal/fsm):
    - [ ] 2.1.1. Создать структуру FSM и интерфейс для работы с состояниями.
    - [ ] 2.1.2. Перенести все константы и обработчики FSM.
    - [ ] 2.1.3. Обновить Handler на использование нового пакета FSM.
- [ ] 2.2. Вынести генерацию клавиатур и сообщений в отдельный пакет (internal/ui или internal/markup):
    - [ ] 2.2.1. Создать функции для генерации всех типов клавиатур и сообщений.
    - [ ] 2.2.2. Перевести Handler на использование этих функций.
- [ ] 2.3. Вынести бизнес-логику рассылок в сервис (internal/service/broadcast.go):
    - [ ] 2.3.1. Создать сервис BroadcastService с методами для создания, редактирования, удаления рассылок.
    - [ ] 2.3.2. Перенести соответствующую логику из Handler.
- [ ] 2.4. Вынести бизнес-логику тарифов в сервис (internal/service/tariff.go):
    - [ ] 2.4.1. Создать сервис TariffService с методами для управления тарифами.
    - [ ] 2.4.2. Перенести соответствующую логику из Handler.
- [ ] 2.5. Вынести работу с кешем в отдельный слой-абстракцию (internal/cache):
    - [ ] 2.5.1. Создать интерфейс и реализацию кеша.
    - [ ] 2.5.2. Перевести Handler и сервисы на работу через этот слой.
- [ ] 2.6. После каждого выноса — убедиться, что функционал не нарушен:
    - [ ] 2.6.1. Запустить тесты и провести ручную проверку.

---

## Этап 3. Внедрение интерфейсов и DI
- [ ] 3.1. Для всех зависимостей Handler определить интерфейсы (репозитории, сервисы, кеш, Telegram Bot API):
    - [ ] 3.1.1. Описать интерфейсы для всех внешних зависимостей.
    - [ ] 3.1.2. Обновить Handler для работы через интерфейсы.
- [ ] 3.2. Перевести Handler на работу через внедрение зависимостей (dependency injection):
    - [ ] 3.2.1. Реализовать конструктор Handler с DI.
    - [ ] 3.2.2. Обновить все места создания Handler в проекте.
- [ ] 3.3. Обеспечить возможность подмены зависимостей для тестирования:
    - [ ] 3.3.1. Создать моки/фейки для интерфейсов.
    - [ ] 3.3.2. Добавить примеры unit-тестов с подменой зависимостей.
- [ ] 3.4. Проверить работоспособность после внедрения DI:
    - [ ] 3.4.1. Запустить тесты и провести ручную проверку.

---

## Этап 4. Унификация и оптимизация
- [ ] 4.1. Вынести повторяющиеся паттерны (логирование, обработка ошибок, удаление/редактирование сообщений) в middleware/утилиты:
    - [ ] 4.1.1. Создать пакет internal/utils или internal/middleware.
    - [ ] 4.1.2. Перевести обработчики на использование утилит.
- [ ] 4.2. Минимизировать дублирование кода:
    - [ ] 4.2.1. Провести ревизию на предмет дублирующихся функций.
    - [ ] 4.2.2. Объединить или вынести повторяющиеся блоки.
- [ ] 4.3. Упростить обработчики: оставить только маршрутизацию и вызовы сервисов:
    - [ ] 4.3.1. Переписать обработчики для делегирования логики в сервисы.
- [ ] 4.4. Проверить, что все обработчики работают корректно:
    - [ ] 4.4.1. Запустить тесты и провести ручную проверку.

---

## Этап 5. Документирование и поддержка
- [ ] 5.1. Описать архитектуру новых модулей и их взаимодействие:
    - [ ] 5.1.1. Нарисовать схему архитектуры (диаграмма, mermaid, draw.io и т.д.).
    - [ ] 5.1.2. Описать назначение каждого пакета и его публичные интерфейсы.
- [ ] 5.2. Добавить комментарии и примеры использования в коде:
    - [ ] 5.2.1. Прокомментировать все публичные методы и структуры.
    - [ ] 5.2.2. Добавить usage- и doc-тесты.
- [ ] 5.3. Провести ревью и согласование с командой:
    - [ ] 5.3.1. Организовать командное ревью изменений.
    - [ ] 5.3.2. Внести правки по итогам обсуждения.
- [ ] 5.4. Зафиксировать финальное состояние и обновить документацию:
    - [ ] 5.4.1. Обновить README и внутренние wiki/документы.
    - [ ] 5.4.2. Зафиксировать финальную архитектуру.

---

# Чеклист для каждого этапа
- [ ] Изменения внесены только в одну зону ответственности
- [ ] Все тесты проходят успешно
- [ ] Ручная проверка ключевых сценариев
- [ ] Нет деградации производительности
- [ ] Документация и комментарии обновлены
- [ ] Изменения согласованы с командой

---

**Внимание:**
- Не переходить к следующему этапу, пока не завершён предыдущий и не подтверждена работоспособность.
- Все изменения должны быть обратимы (использовать git для контроля версий).
- При возникновении ошибок — откатить изменения и провести дополнительный анализ. 