# Руководство по использованию новой архитектуры

## Быстрый старт

### 1. Работа с FSM (Машина состояний)

```go
// Получение текущего состояния
currentState, ok := h.fsm.GetState(userID)
if !ok {
    // Пользователь не имеет активного состояния
    return
}

// Установка нового состояния
err := h.fsm.SetState(userID, fsm.StateBroadcastCreateText)

// Установка состояния с валидацией переходов
err := h.fsm.SetStateWithValidation(userID, fsm.StateBroadcastCreateTime)

// Очистка состояния (возврат в Idle)
err := h.fsm.ClearState(userID)

// Проверка типа состояния
if currentState.IsBroadcastState() {
    // Логика для рассылок
} else if currentState.IsTariffState() {
    // Логика для тарифов
}
```

### 2. Работа с UI (Пользовательский интерфейс)

```go
// Создание основных меню
adminMenu := h.ui.BuildAdminMenu()
broadcastMenu := h.ui.BuildBroadcastMenu()

// Отправка меню
msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
    ChatID:    chatID,
    Text:      adminMenu.Text,
    ParseMode: models.ParseModeHTML,
    ReplyMarkup: models.InlineKeyboardMarkup{
        InlineKeyboard: adminMenu.Keyboard,
    },
})

// Создание меню с пагинацией
paginatedMenu := h.ui.BuildBroadcastListPaginationMenu(page, totalPages)

// Создание стандартных кнопок
backButton := h.ui.BackButton("admin_menu")
cancelButton := h.ui.CancelButton("operation_cancel")
confirmButton := h.ui.ConfirmButton("operation_confirm")
```

### 3. Работа с сервисами

#### BroadcastService

```go
// Создание отложенной рассылки
task, err := h.broadcastService.CreateScheduledBroadcast(ctx, text, sendAt, "all")

// Обновление рассылки
err := h.broadcastService.UpdateBroadcast(ctx, taskID, newText, newSendAt)

// Удаление рассылки
err := h.broadcastService.DeleteBroadcast(ctx, taskID)

// Моментальная рассылка
err := h.broadcastService.SendInstantBroadcast(ctx, b, text, "active")

// Получение всех рассылок
tasks, err := h.broadcastService.GetAllBroadcasts(ctx)

// Валидация времени
err := h.broadcastService.ValidateScheduleTime(scheduleTime)
```

#### TariffService

```go
// Создание тарифа
tariff, err := h.tariffService.CreateTariff(ctx, "1m", "Месячный тариф", 100, 50)

// Обновление тарифа
err := h.tariffService.UpdateTariff(ctx, "1m", "Новое название", 150, 75)

// Активация/деактивация
err := h.tariffService.ActivateTariff(ctx, "1m")
err := h.tariffService.DeactivateTariff(ctx, "1m")

// Получение тарифов
allTariffs, err := h.tariffService.GetAllTariffs(ctx)
activeTariffs, err := h.tariffService.GetActiveTariffs(ctx)

// Валидация данных
err := h.tariffService.ValidateTariffData(code, title, priceRUB, priceStars)
```

### 4. Работа с кешем

```go
// Данные рассылок
h.cacheHelper.SetBroadcastText(userID, text)
text, ok := h.cacheHelper.GetBroadcastText(userID)

h.cacheHelper.SetBroadcastTime(userID, timeStr)
h.cacheHelper.SetBroadcastAudience(userID, "all")

// Данные тарифов
h.cacheHelper.SetTariffCode(userID, "1m")
h.cacheHelper.SetTariffTitle(userID, "Месячный")
h.cacheHelper.SetTariffPriceRUB(userID, 100)

// Очистка данных
h.cacheHelper.ClearBroadcastData(userID)
h.cacheHelper.ClearTariffData(userID)
h.cacheHelper.ClearAllUserData(userID)

// Работа с сообщениями
h.cacheHelper.SetLastMessageID(userID, messageID)
msgID, ok := h.cacheHelper.GetLastMessageID(userID)
```

### 5. Использование утилит

#### MessageUtils

```go
// Создание утилит (обычно в конструкторе Handler)
utils := utils.NewUtilsContainer("Europe/Moscow")

// Отправка сообщений
msg, err := utils.Message.SendHTMLMessage(ctx, b, chatID, text, keyboard)
msg, err := utils.Message.SendMenuMessage(ctx, b, chatID, text, keyboardButtons)

// Сообщения с типами
msg, err := utils.Message.SendSuccessMessage(ctx, b, chatID, "Операция выполнена", backButton)
msg, err := utils.Message.SendErrorMessage(ctx, b, chatID, "Произошла ошибка", backButton)

// Удаление сообщений
err := utils.Message.DeleteMessage(ctx, b, chatID, messageID)
utils.Message.DeleteMessageSafely(ctx, b, chatID, messageID) // без ошибок
utils.Message.DeleteMultipleMessages(ctx, b, chatID, messageIDs)
```

#### ValidationUtils

```go
// Валидация строк
err := utils.Validation.ValidateNotEmpty(value, "поле")
err := utils.Validation.ValidateLength(value, "поле", 1, 100)

// Валидация чисел
err := utils.Validation.ValidatePositiveInt(price, "цена")
price, err := utils.Validation.ParsePositiveInt(priceStr, "цена")

// Валидация специфичных данных
err := utils.Validation.ValidateTariffCode(code)
err := utils.Validation.ValidateBroadcastText(text)
parsedTime, err := utils.Validation.ValidateDateTime(timeStr, "Europe/Moscow")

// Санитизация
cleanStr := utils.Validation.SanitizeString(dirtyStr)
cleanStr, err := utils.Validation.ValidateAndSanitizeString(str, "поле", 1, 100)
```

#### TimeUtils

```go
// Текущее время
now := utils.Time.Now()

// Форматирование
formatted := utils.Time.FormatDateTime(time.Now())
withTZ := utils.Time.FormatDateTimeWithTZ(time.Now())
relative := utils.Time.FormatRelativeDate(time.Now())

// Парсинг
parsedTime, err := utils.Time.ParseDateTime("25.12.2024 15:30")

// Проверки
if utils.Time.IsInPast(someTime) {
    // Время в прошлом
}

// Валидация для планирования
err := utils.Time.ValidateScheduleTime(scheduleTime)
```

## Паттерны использования

### 1. Обработка callback'ов

```go
func (h *Handler) handleBroadcastCallback(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery) {
    switch {
    case callback.Data == "broadcast_create":
        // Устанавливаем состояние
        h.fsm.SetState(callback.From.ID, fsm.StateBroadcastCreateText)
        
        // Очищаем старые данные
        h.cacheHelper.ClearBroadcastData(callback.From.ID)
        
        // Показываем меню
        menu := h.ui.BuildCreateBroadcastMenu()
        utils.Message.EditMenuMessage(ctx, b, callback.Message.Message.Chat.ID, 
            callback.Message.Message.ID, menu.Text, menu.Keyboard)
            
    case strings.HasPrefix(callback.Data, "broadcast_edit_"):
        // Парсим ID из callback
        action, id, page := ui.ParseBroadcastCallback(callback.Data)
        
        // Получаем рассылку
        task, err := h.broadcastService.GetBroadcast(ctx, id)
        if err != nil {
            utils.Message.SendErrorMessage(ctx, b, callback.Message.Message.Chat.ID, 
                "Рассылка не найдена", h.ui.BackButton("broadcast_list"))
            return
        }
        
        // Показываем меню редактирования
        menu := h.ui.BuildEditBroadcastMenu(task, page)
        utils.Message.EditMenuMessage(ctx, b, callback.Message.Message.Chat.ID,
            callback.Message.Message.ID, menu.Text, menu.Keyboard)
    }
}
```

### 2. Обработка текстовых сообщений

```go
func (h *Handler) handleTextMessage(ctx context.Context, b *bot.Bot, update *models.Update) {
    userID := update.Message.From.ID
    currentState, ok := h.fsm.GetState(userID)
    if !ok {
        return // Нет активного состояния
    }
    
    switch currentState {
    case fsm.StateBroadcastCreateText:
        // Валидируем текст
        text := update.Message.Text
        if err := utils.Validation.ValidateBroadcastText(text); err != nil {
            utils.Message.SendErrorMessage(ctx, b, update.Message.Chat.ID, 
                err.Error(), h.ui.CancelButton("broadcast_cancel"))
            return
        }
        
        // Сохраняем в кеш
        h.cacheHelper.SetBroadcastText(userID, text)
        
        // Переходим к следующему состоянию
        h.fsm.SetState(userID, fsm.StateBroadcastCreateTime)
        
        // Показываем следующее меню
        menu := h.ui.BuildBroadcastTimeMenu()
        utils.Message.SendMenuMessage(ctx, b, update.Message.Chat.ID, 
            menu.Text, menu.Keyboard)
            
    case fsm.StateTariffCode:
        // Валидируем код тарифа
        code := update.Message.Text
        if err := utils.Validation.ValidateTariffCode(code); err != nil {
            utils.Message.SendErrorMessage(ctx, b, update.Message.Chat.ID,
                err.Error(), h.ui.CancelButton("tariff_cancel"))
            return
        }
        
        // Проверяем уникальность
        existing, _ := h.tariffService.GetTariff(ctx, code)
        if existing != nil {
            utils.Message.SendErrorMessage(ctx, b, update.Message.Chat.ID,
                "Тариф с таким кодом уже существует", h.ui.CancelButton("tariff_cancel"))
            return
        }
        
        // Сохраняем и переходим дальше
        h.cacheHelper.SetTariffCode(userID, code)
        h.fsm.SetState(userID, fsm.StateTariffTitle)
        
        menu := h.ui.BuildTariffTitleMenu()
        utils.Message.SendMenuMessage(ctx, b, update.Message.Chat.ID,
            menu.Text, menu.Keyboard)
    }
}
```

### 3. Создание новых состояний FSM

```go
// В fsm/fsm.go добавить новое состояние
const (
    // ... существующие состояния
    StateNewFeature State = 5001
)

// Обновить String() метод
func (s State) String() string {
    switch s {
    // ... существующие cases
    case StateNewFeature:
        return "NewFeature"
    }
}

// Добавить в соответствующий метод проверки типа
func (s State) IsNewFeatureState() bool {
    return s == StateNewFeature
}
```

### 4. Добавление новых UI компонентов

```go
// В ui/new_feature.go
func (u *UIBuilder) BuildNewFeatureMenu() MenuResponse {
    return MenuResponse{
        Text: "<b>Новая функция:</b>\nВыберите действие:",
        Keyboard: [][]models.InlineKeyboardButton{
            {{Text: "Действие 1", CallbackData: "new_feature_action1"}},
            {{Text: "Действие 2", CallbackData: "new_feature_action2"}},
            {u.BackButton("main_menu")},
        },
    }
}

// Обновить интерфейс в ui/interface.go
type UI interface {
    // ... существующие методы
    BuildNewFeatureMenu() MenuResponse
}
```

## Лучшие практики

1. **Всегда используйте интерфейсы** для dependency injection
2. **Валидируйте входные данные** перед обработкой
3. **Очищайте кеш** при смене контекста пользователя
4. **Используйте типобезопасные состояния** FSM
5. **Логируйте важные операции** с контекстной информацией
6. **Обрабатывайте ошибки** и показывайте понятные сообщения пользователю
7. **Используйте утилиты** для избежания дублирования кода

## Отладка

```go
// Получение сводки данных пользователя
summary := h.cacheHelper.GetUserDataSummary(userID)
slog.Info("User data summary", "user_id", userID, "data", summary)

// Проверка текущего состояния
currentState, ok := h.fsm.GetState(userID)
slog.Info("Current FSM state", "user_id", userID, "state", currentState, "exists", ok)

// Валидация переходов
if !h.fsm.ValidateTransition(oldState, newState) {
    slog.Warn("Invalid FSM transition", "from", oldState, "to", newState)
}
```
