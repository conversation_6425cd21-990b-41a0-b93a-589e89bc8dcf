package handler

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/cache"
	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/cryptopay"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/fsm"
	"remnawave-tg-shop-bot/internal/payment"
	"remnawave-tg-shop-bot/internal/service"
	"remnawave-tg-shop-bot/internal/sync"
	"remnawave-tg-shop-bot/internal/translation"
	"remnawave-tg-shop-bot/internal/yookasa"
	"strconv"
	"strings"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

type Handler struct {
	customerRepository   *database.CustomerRepository
	purchaseRepository   *database.PurchaseRepository
	cryptoPayClient      *cryptopay.Client
	yookasaClient        *yookasa.Client
	translation          *translation.Manager
	paymentService       *payment.PaymentService
	syncService          *sync.SyncService
	referralRepository   *database.ReferralRepository
	cache                *cache.Cache
	broadcastTaskService *service.BroadcastTaskService
	BroadcastWakeup      chan struct{}
	// Новый репозиторий тарифов
	tariffRepository *database.TariffRepository
	// FSM для управления состояниями
	fsm fsm.FSM
}

func NewHandler(
	syncService *sync.SyncService,
	paymentService *payment.PaymentService,
	translation *translation.Manager,
	customerRepository *database.CustomerRepository,
	purchaseRepository *database.PurchaseRepository,
	cryptoPayClient *cryptopay.Client,
	yookasaClient *yookasa.Client,
	referralRepository *database.ReferralRepository,
	cache *cache.Cache,
	broadcastTaskService *service.BroadcastTaskService,
	broadcastWakeup chan struct{},
	tariffRepository *database.TariffRepository, // новый параметр
) *Handler {
	// Создаем FSM менеджер
	fsmManager := fsm.NewFSMManager(cache)

	return &Handler{
		syncService:          syncService,
		paymentService:       paymentService,
		customerRepository:   customerRepository,
		purchaseRepository:   purchaseRepository,
		cryptoPayClient:      cryptoPayClient,
		yookasaClient:        yookasaClient,
		translation:          translation,
		referralRepository:   referralRepository,
		cache:                cache,
		broadcastTaskService: broadcastTaskService,
		BroadcastWakeup:      broadcastWakeup,
		tariffRepository:     tariffRepository, // инициализация
		fsm:                  fsmManager,       // инициализация FSM
	}
}

// AdminCommandHandler — обработчик команды /admin (только для администратора)
func (h Handler) AdminCommandHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update.CallbackQuery != nil && update.CallbackQuery.Message.Message != nil {
		text, keyboard := buildAdminMenu()
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.CallbackQuery.Message.Message.Chat.ID,
			MessageID: update.CallbackQuery.Message.Message.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка обновления админ-меню (callback)", err)
		} else {
			h.cache.SetInt(9999999+update.CallbackQuery.From.ID, msgEdit.ID)
		}
		return
	}
	if update.Message == nil {
		slog.Warn("AdminCommandHandler: update.Message is nil", "update", update)
		return
	}
	slog.Info("AdminCommandHandler called", "user_id", update.Message.From.ID, "username", update.Message.From.Username, "text", update.Message.Text)
	if update.Message.From.ID != config.GetAdminTelegramId() {
		slog.Warn("AdminCommandHandler: not admin", "user_id", update.Message.From.ID, "admin_id", config.GetAdminTelegramId())
		return
	}
	text, keyboard := buildAdminMenu()
	msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    update.Message.Chat.ID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
	})
	if err != nil {
		slog.Error("Ошибка отправки админ-меню", err)
	} else {
		h.cache.SetInt(9999999+update.Message.From.ID, msgReply.ID)
	}
}

// Старые константы FSM удалены - теперь используется пакет internal/fsm

// AdminCallbackHandler — обработчик callback-ов из админ-меню
func (h Handler) AdminCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update.CallbackQuery == nil || (!strings.HasPrefix(update.CallbackQuery.Data, "admin_") && !strings.HasPrefix(update.CallbackQuery.Data, "tariff_") && !strings.HasPrefix(update.CallbackQuery.Data, "broadcast_") && !strings.HasPrefix(update.CallbackQuery.Data, "instant_broadcast")) {
		return
	}
	// Логируем тип update и callback, а также FSM state для диагностики
	var state int
	if update.CallbackQuery != nil {
		st, ok := h.cache.GetInt(update.CallbackQuery.From.ID)
		if ok {
			state = st
		}
		slog.Info("[AdminCallbackHandler]", "user_id", update.CallbackQuery.From.ID, "state", state, "callback_data", update.CallbackQuery.Data)
	}
	if update == nil {
		slog.Error("[AdminCallbackHandler] update is nil")
		return
	}
	if update.CallbackQuery == nil {
		slog.Error("[AdminCallbackHandler] CallbackQuery is nil", "update", update)
		return
	}
	callback := update.CallbackQuery
	if callback.From.ID == 0 {
		slog.Error("[AdminCallbackHandler] CallbackQuery.From.ID is zero", "callback", callback)
		return
	}
	if callback.Message.Message == nil {
		slog.Error("[AdminCallbackHandler] CallbackQuery.Message.Message is nil", "callback", callback)
		return
	}
	if callback.From.ID != config.GetAdminTelegramId() {
		return
	}

	switch callback.Data {
	case "admin_menu":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		// Очищаем сообщения с тарифами при возврате в главное меню
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		h.cache.SetString(3000000+callback.From.ID, "")
		h.cache.SetString(4000000+callback.From.ID, "")
		text, keyboard := buildAdminMenu()
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки админ-меню (callback)", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case "admin_broadcast":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		h.cache.SetString(3000000+callback.From.ID, "")
		h.cache.SetString(4000000+callback.From.ID, "")
		text, keyboard := buildBroadcastMenu()
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки меню рассылок", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	case "instant_broadcast":
		h.fsm.SetState(callback.From.ID, fsm.StateBroadcastInstantText)
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData1), "")
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData2), "")
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData3), "")
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData4), "")
		text, keyboard := buildInstantBroadcastMenu()
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка запуска моментальной рассылки", err)
		} else {
			h.cache.SetInt(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyLastMsg), msgEdit.ID)
		}
		return
	case "admin_sync":
		h.SyncUsersCommandHandler(ctx, b, update)
		return
	case "broadcast_list":
		h.sendPaginatedBroadcastList(ctx, b, callback, 1)
		return
	case "broadcast_create":
		h.fsm.SetState(callback.From.ID, fsm.StateBroadcastCreateText)
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData1), "") // текст
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData2), "") // время
		text, keyboard := buildCreateBroadcastMenu()
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка обновления меню создания рассылки", err)
		} else {
			h.cache.SetInt(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyLastMsg), msgEdit.ID)
		}
		return
	case "broadcast_create_cancel":
		h.sendPaginatedBroadcastList(ctx, b, callback, 1)
		return
	case "instant_broadcast_send_all", "instant_broadcast_send_unsubscribed":
		// Подтверждение мгновенной рассылки
		text, _ := h.cache.GetString(9000000 + callback.From.ID)
		var users []database.Customer
		var err error
		if callback.Data == "instant_broadcast_send_all" {
			users, err = h.customerRepository.GetAll(ctx)
		} else {
			users, err = h.customerRepository.GetUsersWithoutActiveSubscription(ctx)
		}

		targetAudience := "all"
		if callback.Data == "instant_broadcast_send_unsubscribed" {
			targetAudience = "unsubscribed"
		}

		if err != nil {
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка выборки пользователей.",
			})
			return
		}
		var success, failed int
		var blocked []int64
		for _, user := range users {
			if user.TelegramID == config.GetAdminTelegramId() {
				continue
			}
			_, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: user.TelegramID,
				Text:   text,
			})
			if err != nil {
				failed++
				blocked = append(blocked, user.TelegramID)
			} else {
				success++
			}
			time.Sleep(35 * time.Millisecond)
		}
		now := time.Now()
		msgText := fmt.Sprintf("📢 <b>Рассылка завершена!</b>\n\nТекст: %s\nАудитория: %s\nВремя отправки: %s\n\n✅ Успешно: <b>%d</b>\n🚫 Ошибок: <b>%d</b>", text, TranslateAudience(targetAudience), FormatTimeWithTZ(now), success, failed)
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      msgText,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка обновления сообщения о результатах рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
		}
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(9000000+callback.From.ID, "")
		return
	case "instant_broadcast_cancel":
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		text, keyboard := buildBroadcastMenu()
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка возврата к меню рассылок", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(9000000+callback.From.ID, "")
		return
	case "admin_tariffs":
		// Удаляем предыдущее сообщение (админ-меню)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})
		// Переход на первую страницу тарифов
		h.showTariffsPage(ctx, b, callback, 1)
		return
	case "tariff_add":
		h.fsm.SetState(callback.From.ID, fsm.StateTariffCode)
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData7), "")
		h.cache.SetString(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyTempData7_1), "")
		h.cache.SetString(7000002+callback.From.ID, "")
		h.cache.SetString(7000003+callback.From.ID, "")
		h.cache.SetString(7000004+callback.From.ID, "")
		// Корректно определяем chatID для отправки сообщения
		var chatID int64
		if callback.Message.Message != nil {
			chatID = callback.Message.Message.Chat.ID
			h.clearTariffMessages(ctx, b, callback.From.ID, chatID)
		} else {
			// Если нет сообщения — отправляем в личку админу
			chatID = callback.From.ID
		}
		slog.Info("[TARIFF FSM] Отправляю приглашение к вводу кода тарифа", "chatID", chatID, "userID", callback.From.ID)
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    chatID,
			Text:      "Введите <b>код тарифа</b> (латиница, цифры, например: 1m, 3m, basic):",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("[TARIFF FSM] Ошибка вывода шага ввода кода тарифа", "err", err, "chatID", chatID, "userID", callback.From.ID)
			return
		}
		h.cache.SetInt(fsm.GetCacheKey(callback.From.ID, fsm.CacheKeyLastMsg), msg.ID)
		return
	case "tariff_create":
		// Завершаем FSM и создаём тариф в БД
		code, _ := h.cache.GetString(7000000 + callback.From.ID)
		title, _ := h.cache.GetString(7000001 + callback.From.ID)
		priceRUB, _ := h.cache.GetInt(7000002 + callback.From.ID)
		priceStars, _ := h.cache.GetInt(7000003 + callback.From.ID)
		// Тариф всегда создаётся неактивным
		tariff := &database.Tariff{
			Code:       code,
			Title:      title,
			PriceRUB:   priceRUB,
			PriceStars: priceStars,
			Active:     false,
		}
		_, err := h.tariffRepository.Create(ctx, tariff)
		if err != nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка создания тарифа.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		msg := callback.Message.Message
		_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      "✅ Тариф успешно создан и пока неактивен. Для активации используйте соответствующую кнопку в списке тарифов.",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
				},
			},
		})
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.Delete(7000000 + callback.From.ID)
		h.cache.Delete(7000001 + callback.From.ID)
		h.cache.Delete(7000002 + callback.From.ID)
		h.cache.Delete(7000003 + callback.From.ID)
		h.showTariffsPage(ctx, b, &models.CallbackQuery{From: models.User{ID: callback.From.ID}, Message: callback.Message}, 1)
		return
	case "tariff_update":
		// Завершаем FSM и обновляем тариф в БД
		code, _ := h.cache.GetString(7000000 + callback.From.ID)
		title, _ := h.cache.GetString(7000001 + callback.From.ID)
		priceRUB, _ := h.cache.GetInt(7000002 + callback.From.ID)
		priceStars, _ := h.cache.GetInt(7000003 + callback.From.ID)

		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка обновления тарифа.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}

		tariff.Title = title
		tariff.PriceRUB = priceRUB
		tariff.PriceStars = priceStars

		err = h.tariffRepository.Update(ctx, tariff)
		if err != nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка обновления тарифа.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}

		msg := callback.Message.Message
		_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      "✅ Тариф успешно обновлен.",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
				},
			},
		})
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.Delete(7000000 + callback.From.ID)
		h.cache.Delete(7000001 + callback.From.ID)
		h.cache.Delete(7000002 + callback.From.ID)
		h.cache.Delete(7000003 + callback.From.ID)
		h.showTariffsPage(ctx, b, &models.CallbackQuery{From: models.User{ID: callback.From.ID}, Message: callback.Message}, 1)
		return
	}

	// --- Обработка callback.Data с префиксами ---
	switch {
	case strings.HasPrefix(callback.Data, "broadcast_list_page_"):
		page := 1
		pstr := strings.TrimPrefix(callback.Data, "broadcast_list_page_")
		if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
			page = n
		}
		// --- Удаляем все старые сообщения (меню, этапы редактирования) ---
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		h.sendPaginatedBroadcastList(ctx, b, callback, page)
		return
	case strings.HasPrefix(callback.Data, "broadcast_create_confirm_"):
		if state, ok := h.cache.GetInt(callback.From.ID); !ok || state != adminBroadcastStateCreateReady {
			return
		}
		targetAudience := strings.TrimPrefix(callback.Data, "broadcast_create_confirm_")

		text, _ := h.cache.GetString(1000000 + callback.From.ID)
		timeStr, _ := h.cache.GetString(2000000 + callback.From.ID)
		t, err := time.Parse(time.RFC3339, timeStr)
		if err != nil {
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка парсинга времени. Операция отменена.",
			})
			return
		}
		task := &database.BroadcastTask{
			Message:        text,
			SendAt:         t.UTC(),
			Status:         database.BroadcastTaskStatusPending,
			TargetAudience: targetAudience,
		}
		_, err = h.broadcastTaskService.Create(ctx, task)
		if err != nil {
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка создания рассылки.",
			})
			return
		}
		// --- Сигнал планировщику ---
		if h.BroadcastWakeup != nil {
			select {
			case h.BroadcastWakeup <- struct{}{}:
			default:
			}
		}
		// Показываем сообщение об успехе с кнопкой возврата
		textMenu, keyboard := buildSuccessBroadcastCreatedMenu(targetAudience)
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      textMenu,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки сообщения об успешном создании рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
		}
		// Сброс FSM
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(1000000+callback.From.ID, "")
		h.cache.SetString(2000000+callback.From.ID, "")
		return
	case strings.HasPrefix(callback.Data, "broadcast_delete_confirm_"):
		slog.Info("DELETE_CONFIRM: Handling callback", "data", callback.Data)
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_delete_confirm_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			slog.Error("DELETE_CONFIRM: Failed to parse ID", "idStr", idStr, "error", err)
			return
		}

		slog.Info("DELETE_CONFIRM: Attempting to delete task", "id", id)
		err = h.broadcastTaskService.Delete(ctx, id)
		if err != nil {
			slog.Error("DELETE_CONFIRM: Error deleting broadcast task", "err", err, "task_id", id)
			b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "❌ Ошибка при удалении рассылки.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{
							{Text: "⬅️ К списку", CallbackData: fmt.Sprintf("broadcast_list_page_%d", page)},
						},
					},
				},
			})
			return
		}
		slog.Info("DELETE_CONFIRM: Task deleted successfully", "id", id)

		// --- Сигнал планировщику ---
		if h.BroadcastWakeup != nil {
			select {
			case h.BroadcastWakeup <- struct{}{}:
			default:
			}
		}
		h.sendPaginatedBroadcastList(ctx, b, callback, page)
		return
	case strings.HasPrefix(callback.Data, "broadcast_edit_confirm_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_edit_confirm_")
		// page не используется, убираю
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		text, _ := h.cache.GetString(3000000 + callback.From.ID) // гарантируем, что берём новый текст
		timeStr, _ := h.cache.GetString(4000000 + callback.From.ID)
		t, err := time.Parse(time.RFC3339, timeStr)
		if err != nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка парсинга времени.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке парсинга времени", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil || task == nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Задача не найдена.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке поиска задачи", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			return
		}
		task.Message = text
		task.SendAt = t.UTC()
		err = h.broadcastTaskService.Delete(ctx, id)
		if err == nil {
			_, err = h.broadcastTaskService.Create(ctx, task)
		}
		if err != nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Ошибка сохранения изменений.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке сохранения изменений", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
		} else {
			// Показываем сообщение об успехе с кнопкой возврата
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "✅ Изменения успешно сохранены!",
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об успешном сохранении изменений", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			// --- Сигнал планировщику ---
			if h.BroadcastWakeup != nil {
				select {
				case h.BroadcastWakeup <- struct{}{}:
				default:
				}
			}
		}
		h.cache.SetInt(callback.From.ID, 0)
		h.cache.SetString(3000000+callback.From.ID, "")
		h.cache.SetString(4000000+callback.From.ID, "")
		h.cache.Delete(7000000 + callback.From.ID)
		return
	case strings.HasPrefix(callback.Data, "broadcast_delete_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_delete_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		msg, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
			Text:      "Удалить рассылку?",
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "Да", CallbackData: fmt.Sprintf("broadcast_delete_confirm_%d_p%d", id, page)}},
					{{Text: "Нет", CallbackData: fmt.Sprintf("broadcast_revert_%d_p%d", id, page)}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки сообщения для подтверждения удаления рассылки", "err", err)
		} else if msg != nil {
			// Сохраняем ID сообщения с подтверждением, чтобы его можно было корректно обработать
			h.cache.Set(6000000+callback.From.ID, msg.ID)
		}
		return
	case strings.HasPrefix(callback.Data, "broadcast_revert_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_revert_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil {
			return // или обработать ошибку
		}
		text, keyboard := buildSingleBroadcastEntry(*task, page)
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:      callback.Message.Message.Chat.ID,
			MessageID:   callback.Message.Message.ID,
			Text:        text,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		if err != nil {
			slog.Error("Ошибка возврата к сообщению о рассылке", err)
		}
		return
	case strings.HasPrefix(callback.Data, "broadcast_send_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_send_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil || task == nil {
			msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: callback.Message.Message.Chat.ID,
				Text:   "Задача не найдена.",
			})
			if err != nil {
				slog.Error("Ошибка отправки сообщения об ошибке поиска задачи", err)
			} else if msgReply != nil {
				h.cache.SetInt(9999999+callback.From.ID, msgReply.ID)
			}
			return
		}
		users, err := h.customerRepository.GetAll(ctx)
		if err != nil {
			msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: callback.Message.Message.Chat.ID,
				Text:   "Ошибка выборки пользователей.",
			})
			if err != nil {
				slog.Error("Ошибка отправки сообщения об ошибке выборки пользователей", err)
			} else if msgReply != nil {
				h.cache.SetInt(9999999+callback.From.ID, msgReply.ID)
			}
			return
		}
		var success, failed int
		for _, user := range users {
			if user.TelegramID == config.GetAdminTelegramId() {
				continue
			}
			_, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: user.TelegramID,
				Text:   task.Message,
			})
			if err != nil {
				failed++
			} else {
				success++
			}
			time.Sleep(35 * time.Millisecond)
		}
		msgText := fmt.Sprintf("📢 <b>Отложенная рассылка завершена!</b>\n\nID: %d\nТекст: %s\nАудитория: %s\nВремя (запланировано): %s\nВремя (отправлено): %s\n\n✅ Успешно: <b>%d</b>\n🚫 Ошибок: <b>%d</b>", task.ID, task.Message, TranslateAudience(task.TargetAudience), FormatTimeWithTZ(task.SendAt), FormatTimeWithTZ(time.Now()), success, failed)
		msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      msgText,
			ParseMode: models.ParseModeHTML,
		})
		if err != nil {
			slog.Error("Ошибка отправки сообщения об успешной отправке рассылки", err)
		} else {
			h.cache.SetInt(9999999+callback.From.ID, msgReply.ID)
		}
		// Меняем статус задачи на "Отправлено"
		err = h.broadcastTaskService.UpdateStatus(ctx, task.ID, database.BroadcastTaskStatusSent)
		if err != nil {
			slog.Error("Ошибка обновления статуса рассылки на 'Отправлено'", err)
		}
		h.AdminCallbackHandler(ctx, b, &models.Update{CallbackQuery: &models.CallbackQuery{From: callback.From, Message: callback.Message, Data: fmt.Sprintf("broadcast_list_page_%d", page)}})
		return
	case strings.HasPrefix(callback.Data, "broadcast_edit_"):
		idStr := strings.TrimPrefix(strings.SplitN(callback.Data, "_p", 2)[0], "broadcast_edit_")
		page := 1
		if strings.Contains(callback.Data, "_p") {
			pstr := strings.SplitN(callback.Data, "_p", 2)[1]
			if n, err := strconv.Atoi(pstr); err == nil && n > 0 {
				page = n
			}
		}
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			return
		}
		task, err := h.broadcastTaskService.GetByID(ctx, id)
		if err != nil || task == nil {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Message.Message.Chat.ID,
				MessageID: callback.Message.Message.ID,
				Text:      "Задача не найдена.",
			})
			if err != nil {
				slog.Error("Ошибка обновления сообщения об ошибке поиска задачи", err)
			} else {
				h.cache.SetInt(9999999+callback.From.ID, msgEdit.ID)
			}
			return
		}
		// --- Удаляем все старые сообщения (меню, списки рассылок) ---
		h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
		// --- Сохраняем id рассылки отдельно и сбрасываем FSM на этап ввода текста ---
		h.cache.SetInt(callback.From.ID, adminBroadcastStateEditText)
		h.cache.SetInt(7000000+callback.From.ID, int(id))
		h.cache.SetString(3000000+callback.From.ID, task.Message)
		h.cache.SetString(4000000+callback.From.ID, task.SendAt.Format(time.RFC3339))
		h.cache.SetString(1000000+callback.From.ID, "") // очищаем временные значения
		h.cache.SetString(2000000+callback.From.ID, "")
		slog.Info("[AdminCallbackHandler] переход к редактированию", "user_id", callback.From.ID, "state", adminBroadcastStateEditText, "callback_data", callback.Data)
		text, keyboard := buildEditBroadcastMenu(task, page)
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			Text:      text,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки меню редактирования рассылки", err)
		} else if msg != nil {
			h.cache.SetInt(9999999+callback.From.ID, msg.ID)
		}
		return
	// Обработка callback для активации/деактивации тарифа
	case strings.HasPrefix(callback.Data, "tariff_toggle_"):
		data := strings.TrimPrefix(callback.Data, "tariff_toggle_")
		code := data
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
		}
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка: тариф не найден.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		if tariff.Active {
			// Проверяем, не последний ли это активный тариф
			tariffs, err := h.tariffRepository.GetAll(ctx, true)
			if err != nil {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Ошибка получения списка тарифов.",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			if len(tariffs) == 1 && tariffs[0].ID == tariff.ID {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Нельзя деактивировать последний активный тариф!",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			// Деактивируем тариф
			err = h.tariffRepository.SetActive(ctx, tariff.ID, false)
			if err != nil {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Ошибка при изменении статуса тарифа.",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
		} else {
			// Просто активируем тариф
			err = h.tariffRepository.SetActive(ctx, tariff.ID, true)
			if err != nil {
				msg := callback.Message.Message
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msg.ID,
					Text:      "Ошибка при изменении статуса тарифа.",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
		}
		// После изменения статуса — обновляем меню тарифов
		page := 1
		if idx := strings.LastIndex(callback.Data, "_p"); idx != -1 {
			if p, err := strconv.Atoi(callback.Data[idx+2:]); err == nil {
				page = p
			}
		}
		h.showTariffsPage(ctx, b, callback, page)
		return
	// Обработка callback для редактирования тарифа
	case strings.HasPrefix(callback.Data, "tariff_edit_"):
		data := strings.TrimPrefix(callback.Data, "tariff_edit_")
		code := data
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
		}
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка: тариф не найден.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		// Очищаем все сообщения с тарифами, кроме текущего, перед редактированием
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID, callback.Message.Message.ID)
		// Обновляем кеш сообщения для FSM
		h.cache.SetInt(9999999+callback.From.ID, callback.Message.Message.ID)
		// Предзаполняем кеш текущими значениями тарифа
		h.cache.SetString(7000000+callback.From.ID, tariff.Code)
		h.cache.SetString(7000001+callback.From.ID, tariff.Title)
		h.cache.SetInt(7000002+callback.From.ID, tariff.PriceRUB)
		h.cache.SetInt(7000003+callback.From.ID, tariff.PriceStars)
		h.cache.SetInt(7000004+callback.From.ID, boolToInt(tariff.Active))
		h.cache.SetInt(callback.From.ID, tariffFSMStateEditTitle) // начинаем с редактирования названия
		msg := callback.Message.Message
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      fmt.Sprintf("Редактирование тарифа <b>%s</b> (код: <code>%s</code>)\n\nВведите новое <b>название тарифа</b> или оставьте текущее:", tariff.Title, tariff.Code),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка вывода шага редактирования названия тарифа", err)
		}
		return
	// Обработка callback подтверждения удаления тарифа
	case strings.HasPrefix(callback.Data, "tariff_delete_confirm_"):
		// Сначала удаляем сообщение с кнопками подтверждения
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    callback.Message.Message.Chat.ID,
			MessageID: callback.Message.Message.ID,
		})

		data := strings.TrimPrefix(callback.Data, "tariff_delete_confirm_")
		code := data
		page := 1
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
			if p, err := strconv.Atoi(data[idx+2:]); err == nil {
				page = p
			}
		}

		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			// Если тариф не найден, просто возвращаемся на страницу тарифов
			h.showTariffsPage(ctx, b, callback, page)
			return
		}

		err = h.tariffRepository.Delete(ctx, tariff.ID)
		if err != nil {
			// В случае ошибки удаления, также возвращаемся на страницу тарифов
			h.showTariffsPage(ctx, b, callback, page)
			return
		}

		// После успешного удаления, показываем обновленный список тарифов
		h.showTariffsPage(ctx, b, callback, page)
		return

	// Обработка callback для удаления тарифа (запрос подтверждения)
	case strings.HasPrefix(callback.Data, "tariff_delete_"):
		data := strings.TrimPrefix(callback.Data, "tariff_delete_")
		code := data
		page := 1
		if idx := strings.LastIndex(data, "_p"); idx != -1 {
			code = data[:idx]
			if p, err := strconv.Atoi(data[idx+2:]); err == nil {
				page = p
			}
		}

		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			msg := callback.Message.Message
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msg.ID,
				Text:      "Ошибка: тариф не найден.",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "⬅️ Назад", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page)}},
					},
				},
			})
			return
		}

		msg := callback.Message.Message
		_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
			Text:      fmt.Sprintf("Вы действительно хотите удалить тариф <b>%s</b> (код: <code>%s</code>)? Это действие необратимо.", tariff.Title, tariff.Code),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{
						{Text: "🗑 Подтвердить удаление", CallbackData: fmt.Sprintf("tariff_delete_confirm_%s_p%d", tariff.Code, page)},
						{Text: "❌ Отмена", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page)},
					},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка вывода подтверждения удаления тарифа", err)
		}
		return
	case strings.HasPrefix(callback.Data, "admin_tariffs_page_"):
		pageStr := strings.TrimPrefix(callback.Data, "admin_tariffs_page_")
		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			page = 1
		}
		h.showTariffsPage(ctx, b, callback, page)
		return
	case strings.HasPrefix(callback.Data, "admin_tariffs_page_0") || (callback.Data == "admin_menu" && strings.Contains(callback.Message.Message.Text, "Тарифы")):
		// Очищаем сообщения с тарифами при возврате назад из тарифов
		h.clearTariffMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)
	}
}

// AdminBroadcastTextHandler — обработчик текста рассылки
func (h Handler) AdminBroadcastTextHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	// Подробное логирование для диагностики FSM
	if update == nil || update.Message == nil {
		slog.Error("[AdminBroadcastTextHandler] update or message is nil", "update", update)
		return
	}
	userID := update.Message.From.ID
	currentState, ok := h.fsm.GetState(userID)
	if !ok {
		slog.Warn("[AdminBroadcastTextHandler] FSM state not found", "user_id", userID)
		return
	}
	slog.Info("[AdminBroadcastTextHandler] вход", "user_id", userID, "state", currentState, "text", update.Message.Text)
	// FSM для мгновенной рассылки
	if currentState == fsm.StateBroadcastInstantText {
		text := update.Message.Text
		h.cache.SetString(fsm.GetCacheKey(userID, fsm.CacheKeyTempData9), text)
		h.fsm.SetState(userID, fsm.StateBroadcastInstantTargetSelect)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			msgReply, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID: update.Message.Chat.ID,
				Text:   fmt.Sprintf("Текст рассылки:\n%s\n\nВыберите аудиторию для рассылки:", text),
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "🗣️ Всем пользователям", CallbackData: "instant_broadcast_send_all"}},
						{{Text: "👤 Без подписки", CallbackData: "instant_broadcast_send_unsubscribed"}},
						{{Text: "❌ Отмена", CallbackData: "instant_broadcast_cancel"}},
					},
				},
			})
			if err != nil {
				slog.Error("Ошибка отправки сообщения для подтверждения моментальной рассылки", err)
				return
			}
			if msgReply != nil {
				h.cache.SetInt(9999999+userID, msgReply.ID)
			}
			return
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Текст рассылки:\n%s\n\nВыберите аудиторию для рассылки:", text),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "🗣️ Всем пользователям", CallbackData: "instant_broadcast_send_all"}},
					{{Text: "👤 Без подписки", CallbackData: "instant_broadcast_send_unsubscribed"}},
					{{Text: "❌ Отмена", CallbackData: "instant_broadcast_cancel"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для подтверждения моментальной рассылки", err)
		} else if msgEdit != nil {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		return
	}
	// FSM для редактирования рассылки
	if currentState == fsm.StateBroadcastEditText {
		// Ввод нового текста (может быть любым, даже если это дата/время)
		// id рассылки не используется на этом этапе, поэтому не объявляем переменную id
		text := update.Message.Text
		h.cache.SetString(3000000+userID, text)
		msgID, ok := h.cache.GetInt(9999999 + userID)
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
		}
		oldTime, _ := h.cache.GetString(4000000 + userID)
		editText := "Текст сохранён. Теперь введите новую дату и время рассылки в формате ДД.ММ.ГГГГ ЧЧ:ММ (например, 31.12.2025 23:59).\nТекущее время: " + FormatTimeWithTZMustParse(oldTime) + "\nЕсли хотите оставить текущее время, просто повторите его."
		if ok {
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      editText,
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для ввода времени при редактировании", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
		} else {
			// Если msgID не найден, отправляем новое сообщение и удаляем старое меню (если возможно)
			msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
				ChatID:    update.Message.Chat.ID,
				Text:      editText,
				ParseMode: models.ParseModeHTML,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка отправки нового сообщения для ввода времени при редактировании", err)
			} else if msg != nil {
				h.cache.SetInt(9999999+userID, msg.ID)
			}
			// Пробуем удалить старое меню, если оно есть (например, из callback)
			if update.CallbackQuery != nil && update.CallbackQuery.Message.Message != nil {
				_, errDel := b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    update.CallbackQuery.Message.Message.Chat.ID,
					MessageID: update.CallbackQuery.Message.Message.ID,
				})
				if errDel != nil {
					slog.Warn("Не удалось удалить старое меню при переходе к этапу времени", "err", errDel)
				}
			}
		}
		// Всегда переводим FSM только на этап времени, независимо от содержимого текста
		h.cache.SetInt(userID, adminBroadcastStateEditTime)
		// Удаляем сообщение пользователя с текстом
		_, errDelUser := b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})
		if errDelUser != nil {
			slog.Warn("Не удалось удалить сообщение пользователя с текстом при редактировании", "err", errDelUser)
		}
		return
	}
	if state == adminBroadcastStateEditTime {
		// Ввод нового времени
		idInt, _ := h.cache.GetInt(7000000 + userID)
		id := int64(idInt)
		input := update.Message.Text
		// Получаем текст рассылки, чтобы не допустить совпадения
		textMsg, _ := h.cache.GetString(3000000 + userID)
		if input == textMsg {
			// Если пользователь повторяет текст, который только что ввёл, не принимаем это как дату
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: update.Message.ID,
			})
			msgID, ok := h.cache.GetInt(9999999 + userID)
			if !ok {
				return
			}
			text := "Введённое время совпадает с текстом рассылки. Пожалуйста, введите дату и время, отличные от текста рассылки (ДД.ММ.ГГГГ ЧЧ:ММ):"
			keyboard := [][]models.InlineKeyboardButton{
				{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
			}
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      text,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для повторного ввода времени при совпадении с текстом", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
			return
		}
		loc, _ := time.LoadLocation(config.TimeZone())
		now := time.Now().In(loc)
		t, err := time.ParseInLocation("02.01.2006 15:04", input, loc)
		if err != nil || !t.After(now) || t.After(now.Add(48*time.Hour)) {
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: update.Message.ID,
			})
			msgID, ok := h.cache.GetInt(9999999 + userID)
			if !ok {
				return
			}
			var errorText string
			if err != nil {
				errorText = "Неверный формат даты/времени."
			} else if !t.After(now) {
				errorText = "Рассылка должна быть не ранее текущей даты."
			} else {
				errorText = "Рассылка должна быть не позднее 48 часов от текущей даты."
			}
			text := fmt.Sprintf("%s\n\nПопробуйте ещё раз (ДД.ММ.ГГГГ ЧЧ:ММ):", errorText)
			keyboard := [][]models.InlineKeyboardButton{
				{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
			}
			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      text,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для повторного ввода времени при редактировании", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
			return
		}
		// Сохраняем новое время
		h.cache.SetString(4000000+userID, t.Format(time.RFC3339))
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			return
		}
		text, _ := h.cache.GetString(3000000 + userID)
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Сохранить изменения?\nНовый текст: %s\nНовое время: %s", text, FormatTimeWithTZ(t)),
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Сохранить", CallbackData: fmt.Sprintf("broadcast_edit_confirm_%d", id)}},
					{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для подтверждения редактирования рассылки", err)
		} else {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		h.cache.SetInt(userID, adminBroadcastStateEditReady)
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})
		return
	}
	if state == adminBroadcastStateInstantConfirm {
		// Ожидаем только callback, игнорируем текст
		return
	}
	// FSM создания новой рассылки
	switch state {
	case adminBroadcastStateCreateText:
		text := update.Message.Text
		h.cache.SetString(1000000+userID, text)
		h.cache.SetInt(userID, adminBroadcastStateCreateTime)

		// Удаляем сообщение пользователя с текстом рассылки
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})

		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			return
		}

		// Здесь также можно заменить на EditMessageText, если требуется, чтобы всё было в одном сообщении
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      "Введите дату и время рассылки в формате ДД.ММ.ГГГГ ЧЧ:ММ (например, 31.12.2025 23:59). Время указывается по " + formatCurrentTZLabel() + ":",
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для ввода времени рассылки", err)
		} else if msgEdit != nil {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		return
	case adminBroadcastStateCreateTime:
		input := update.Message.Text
		loc, _ := time.LoadLocation(config.TimeZone())
		now := time.Now().In(loc)
		t, err := time.ParseInLocation("02.01.2006 15:04", input, loc)
		if err != nil || !t.After(now) || t.After(now.Add(48*time.Hour)) {
			// Удаляем сообщение пользователя
			b.DeleteMessage(ctx, &bot.DeleteMessageParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: update.Message.ID,
			})

			// Получаем ID сообщения бота для редактирования
			msgID, ok := h.cache.GetInt(9999999 + userID)
			if !ok {
				slog.Error("Не удалось получить ID сообщения для редактирования")
				return
			}

			var errorText string
			if err != nil {
				errorText = "Неверный формат даты/времени."
			} else if !t.After(now) {
				errorText = "Рассылка должна быть не ранее текущий даты."
			} else {
				errorText = "Рассылка должна быть не позднее 48 часов от текущей даты."
			}

			text := fmt.Sprintf("%s\n\nПопробуйте ещё раз (ДД.ММ.ГГГГ ЧЧ:ММ):", errorText)
			keyboard := [][]models.InlineKeyboardButton{
				{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
			}

			msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    update.Message.Chat.ID,
				MessageID: msgID,
				Text:      text,
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: keyboard,
				},
			})
			if err != nil {
				slog.Error("Ошибка редактирования сообщения для повторного ввода времени", "err", err)
			} else {
				h.cache.SetInt(9999999+userID, msgEdit.ID)
			}
			return
		}

		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: update.Message.ID,
		})

		// Получаем ID сообщения бота для редактирования
		msgID, ok := h.cache.GetInt(9999999 + userID)
		if !ok {
			slog.Error("Не удалось получить ID сообщения для редактирования")
			return
		}

		h.cache.SetString(2000000+userID, t.Format(time.RFC3339))
		text, _ := h.cache.GetString(1000000 + userID)
		msgEditText := fmt.Sprintf("Создать рассылку?\nТекст: %s\nВремя: %s\n\nВыберите аудиторию:", text, FormatTimeWithTZ(t))
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: "🗣️ Всем пользователям", CallbackData: "broadcast_create_confirm_all"}},
			{{Text: "👤 Без подписки", CallbackData: "broadcast_create_confirm_unsubscribed"}},
			{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
		}
		msgEdit, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    update.Message.Chat.ID,
			MessageID: msgID,
			Text:      msgEditText,
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка редактирования сообщения для подтверждения создания рассылки", err)
		} else {
			h.cache.SetInt(9999999+userID, msgEdit.ID)
		}
		h.cache.SetInt(userID, adminBroadcastStateCreateReady)
		return
	case adminBroadcastStateCreateReady:
		// Ожидаем только callback, игнорируем текст
		return
	}
}

func (h *Handler) AdminTariffTextHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update == nil || update.Message == nil {
		slog.Error("[AdminTariffTextHandler] update or message is nil", "update", update)
		return
	}
	userID := update.Message.From.ID
	state, ok := h.cache.GetInt(userID)
	if !ok {
		slog.Warn("[AdminTariffTextHandler] FSM state not found", "user_id", userID)
		return
	}
	msgID, ok := h.cache.GetInt(9999999 + userID)
	if !ok {
		slog.Warn("[AdminTariffTextHandler] message id not found", "user_id", userID)
		return
	}
	msg := update.Message

	switch state {
	case tariffFSMStateEditTitle:
		title := msg.Text
		if len(title) == 0 || len(title) > 64 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Название тарифа не должно быть пустым и не должно превышать 64 символа. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		h.cache.SetString(7000001+userID, title)
		h.cache.SetInt(userID, tariffFSMStateEditPriceRUB)
		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>новую цену в рублях</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в рублях", err)
		}
		return
	case tariffFSMStateEditPriceRUB:
		if msg.Text != "" {
			priceRUB, err := strconv.Atoi(msg.Text)
			if err != nil || priceRUB <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в рублях должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000002+userID, priceRUB)
		}
		h.cache.SetInt(userID, tariffFSMStateEditPriceStars)
		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>новую цену в звёздах</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в звёздах", err)
		}
		return
	case tariffFSMStateEditPriceStars:
		if msg.Text != "" {
			priceStars, err := strconv.Atoi(msg.Text)
			if err != nil || priceStars <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в звёздах должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000003+userID, priceStars)
		}
		h.cache.SetInt(userID, tariffFSMStateEditConfirm)
		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		// Следующий шаг — подтверждение
		code, _ := h.cache.GetString(7000000 + userID)
		title, _ := h.cache.GetString(7000001 + userID)
		priceRUB, _ := h.cache.GetInt(7000002 + userID)
		priceStars, _ := h.cache.GetInt(7000003 + userID)
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Сохранить изменения тарифа?\nКод: <code>%s</code>\nНазвание: %s\nЦена: %d₽ / %d⭐", code, title, priceRUB, priceStars),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Сохранить", CallbackData: "tariff_update"}, {Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага подтверждения редактирования тарифа", err)
		}
		return
	case tariffFSMStateCode:
		code := msg.Text
		if len(code) < 2 || len(code) > 16 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Код тарифа должен быть от 2 до 16 символов. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		for _, c := range code {
			if !(c >= 'a' && c <= 'z') && !(c >= 'A' && c <= 'Z') && !(c >= '0' && c <= '9') && c != '_' && c != '-' {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Код может содержать только латинские буквы, цифры, -, _ . Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
		}
		tariff, _ := h.tariffRepository.GetByCode(ctx, code)
		if tariff != nil {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Тариф с таким кодом уже существует. Введите другой код:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}

		h.cache.SetString(7000000+userID, code)
		h.cache.SetInt(userID, tariffFSMStateTitle)
		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>название тарифа</b> (до 64 символов):",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода названия тарифа", err)
		}
		return
	case tariffFSMStateTitle:
		title := msg.Text
		if len(title) == 0 || len(title) > 64 {
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    msg.Chat.ID,
				MessageID: msgID,
				Text:      "Название тарифа не должно быть пустым и не должно превышать 64 символа. Введите снова:",
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
					},
				},
			})
			return
		}
		h.cache.SetString(7000001+userID, title)
		h.cache.SetInt(userID, tariffFSMStatePriceRUB)
		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>цену в рублях</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в рублях", err)
		}
		return
	case tariffFSMStatePriceRUB:
		if msg.Text != "" {
			priceRUB, err := strconv.Atoi(msg.Text)
			if err != nil || priceRUB <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в рублях должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000002+userID, priceRUB)
		}
		h.cache.SetInt(userID, tariffFSMStatePriceStars)
		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      "Введите <b>цену в звёздах</b>:",
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага ввода цены в звёздах", err)
		}
		return
	case tariffFSMStatePriceStars:
		if msg.Text != "" {
			priceStars, err := strconv.Atoi(msg.Text)
			if err != nil || priceStars <= 0 {
				_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
					ChatID:    msg.Chat.ID,
					MessageID: msgID,
					Text:      "Цена в звёздах должна быть положительным целым числом. Введите снова:",
					ReplyMarkup: models.InlineKeyboardMarkup{
						InlineKeyboard: [][]models.InlineKeyboardButton{
							{{Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
						},
					},
				})
				return
			}
			h.cache.SetInt(7000003+userID, priceStars)
		}
		h.cache.SetInt(userID, tariffFSMStateConfirm)
		// Удаляем сообщение пользователя
		b.DeleteMessage(ctx, &bot.DeleteMessageParams{
			ChatID:    msg.Chat.ID,
			MessageID: msg.ID,
		})
		// Следующий шаг — статус тарифа
		code, _ := h.cache.GetString(7000000 + userID)
		title, _ := h.cache.GetString(7000001 + userID)
		priceRUB, _ := h.cache.GetInt(7000002 + userID)
		priceStars, _ := h.cache.GetInt(7000003 + userID)
		_, err := b.EditMessageText(ctx, &bot.EditMessageTextParams{
			ChatID:    msg.Chat.ID,
			MessageID: msgID,
			Text:      fmt.Sprintf("Установите <b>статус тарифа</b>:\nКод: <code>%s</code>\nНазвание: %s\nЦена: %d₽ / %d⭐", code, title, priceRUB, priceStars),
			ParseMode: models.ParseModeHTML,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "💾 Создать", CallbackData: "tariff_create"}, {Text: "❌ Отмена", CallbackData: "admin_tariffs"}},
				},
			},
		})
		if err != nil {
			slog.Error("Ошибка шага выбора статуса тарифа", err)
		}
		return
	}
}

// AdminTextHandler — универсальный обработчик текстовых сообщений от администратора.
func (h *Handler) AdminTextHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	if update == nil || update.Message == nil {
		slog.Error("[AdminTextHandler] update or message is nil", "update", update)
		return
	}
	userID := update.Message.From.ID
	state, ok := h.cache.GetInt(userID)
	if !ok {
		slog.Warn("[AdminTextHandler] FSM state not found", "user_id", userID)
		return
	}

	// Маршрутизация в зависимости от состояния FSM
	if (state >= tariffFSMStateCode && state <= tariffFSMStateConfirm) || (state >= tariffFSMStateEditTitle && state <= tariffFSMStateEditConfirm) {
		h.AdminTariffTextHandler(ctx, b, update)
	} else {
		h.AdminBroadcastTextHandler(ctx, b, update)
	}
}

// FormatTimeWithTZ форматирует время с учётом часового пояса из конфига и добавляет смещение UTC
func FormatTimeWithTZ(t time.Time) string {
	loc, err := time.LoadLocation(config.TimeZone())
	if err != nil {
		loc = time.FixedZone("UTC+3", 3*60*60) // fallback
	}
	tInLoc := t.In(loc)
	_, offset := tInLoc.Zone()
	hours := offset / 3600
	var sign string
	if hours >= 0 {
		sign = "+"
	} else {
		sign = "-"
	}
	return tInLoc.Format("02.01.2006 15:04") +
		fmt.Sprintf(" (UTC%s%02d)", sign, abs(hours))
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// formatCurrentTZLabel возвращает строку вида UTC+03:00 для текущего пояса
func formatCurrentTZLabel() string {
	loc, err := time.LoadLocation(config.TimeZone())
	if err != nil {
		return "UTC+03:00"
	}
	// Используем 1 января, чтобы не зависеть от перехода на летнее/зимнее время
	t := time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, loc)
	_, offset := t.Zone()
	hours := offset / 3600
	minutes := (offset % 3600) / 60
	return fmt.Sprintf("UTC%+03d:%02d", hours, minutes)
}

// --- Пагинация рассылок ---
const broadcastsPerPage = 3

func (h *Handler) clearBroadcastMessages(ctx context.Context, b *bot.Bot, userID, chatID int64, exceptMsgID ...int) {
	if oldMsgIDs, ok := h.cache.Get(5000000 + userID); ok {
		if ids, ok := oldMsgIDs.([]int); ok {
			for _, id := range ids {
				skip := false
				for _, except := range exceptMsgID {
					if id == except {
						skip = true
						break
					}
				}
				if skip {
					continue
				}
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    chatID,
					MessageID: id,
				})
			}
		}
		h.cache.Delete(5000000 + userID)
	}
}

func getBroadcastPage(tasks []database.BroadcastTask, page int) ([]database.BroadcastTask, int) {
	if page < 1 {
		page = 1
	}
	total := len(tasks)
	start := (page - 1) * broadcastsPerPage
	if start >= total {
		return nil, (total + broadcastsPerPage - 1) / broadcastsPerPage
	}
	end := start + broadcastsPerPage
	if end > total {
		end = total
	}
	return tasks[start:end], (total + broadcastsPerPage - 1) / broadcastsPerPage
}

// --- Генерация главного админ-меню ---
func buildAdminMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Админ-меню:</b>", [][]models.InlineKeyboardButton{
		{{Text: "🔄 Синхронизация", CallbackData: "admin_sync"}},
		{{Text: "📢 Рассылка", CallbackData: "admin_broadcast"}},
		{{Text: "💸 Тарифы", CallbackData: "admin_tariffs"}}, // Новая кнопка для управления тарифами
	}
}

// --- Генерация подменю рассылок ---
func buildBroadcastMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Меню рассылок:</b>", [][]models.InlineKeyboardButton{
		{{Text: "⚡ Моментальная рассылка", CallbackData: "instant_broadcast"}},
		{{Text: "🕒 Отложенные рассылки", CallbackData: "broadcast_list"}},
		{{Text: "⬅️ Назад", CallbackData: "admin_menu"}},
	}
}

func buildInstantBroadcastMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Моментальная рассылка:</b>\nВведите текст рассылки (будет отправлен сразу после подтверждения):", [][]models.InlineKeyboardButton{
		{{Text: "❌ Отмена", CallbackData: "admin_broadcast"}},
	}
}

// --- Генерация подменю создания рассылки ---
func buildCreateBroadcastMenu() (string, [][]models.InlineKeyboardButton) {
	return "<b>Создание новой рассылки:</b>\nВведите текст рассылки:", [][]models.InlineKeyboardButton{
		{{Text: "❌ Отмена", CallbackData: "broadcast_create_cancel"}},
	}
}

// --- Генерация подменю редактирования рассылки ---
func buildEditBroadcastMenu(task *database.BroadcastTask, page int) (string, [][]models.InlineKeyboardButton) {
	// Обновлено: добавлено пояснение для пользователя
	text := fmt.Sprintf("<b>Редактирование рассылки:</b>\nТекущий текст: %s\nВведите новый текст рассылки (можно ввести любой текст, даже если он похож на дату/время):", task.Message)
	return text, [][]models.InlineKeyboardButton{
		{{Text: "❌ Отмена", CallbackData: "broadcast_list"}},
	}
}

// --- Генерация меню отложенных рассылок ---
func (h *Handler) sendPaginatedBroadcastList(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery, page int) {
	userID := callback.From.ID // определяем userID для использования ниже
	// Сначала удаляем старое сообщение с кнопками, чтобы новое появилось внизу
	b.DeleteMessage(ctx, &bot.DeleteMessageParams{
		ChatID:    callback.Message.Message.Chat.ID,
		MessageID: callback.Message.Message.ID,
	})

	// Очищаем предыдущие сообщения с задачами (если они есть)
	h.clearBroadcastMessages(ctx, b, callback.From.ID, callback.Message.Message.Chat.ID)

	tasks, err := h.broadcastTaskService.GetAll(ctx)
	if err != nil {
		// Так как мы удалили исходное сообщение, отправляем новое с ошибкой
		msg, _ := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID: callback.Message.Message.Chat.ID,
			Text:   "Ошибка получения задач рассылки.",
		})
		if msg != nil {
			// Сохраняем ID, чтобы его можно было удалить при следующем действии
			h.cache.Set(5000000+callback.From.ID, []int{msg.ID})
		}
		return
	}

	pageTasks, totalPages := getBroadcastPage(tasks, page)
	var newMessageIDs []int

	// Отправляем новые сообщения для каждой задачи на текущей странице
	for _, task := range pageTasks {
		text, keyboard := buildSingleBroadcastEntry(task, page)
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      callback.Message.Message.Chat.ID,
			Text:        text,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		if err == nil && msg != nil {
			newMessageIDs = append(newMessageIDs, msg.ID)
		}
	}

	// Создаем и отправляем новое меню пагинации, которое окажется внизу списка
	text, keyboard := buildBroadcastListPaginationMenu(page, totalPages)
	msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      callback.Message.Message.Chat.ID,
		Text:        text,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})
	if err != nil {
		slog.Error("Не удалось отправить сообщение пагинации", "err", err)
	} else if msg != nil {
		// Добавляем ID меню пагинации к списку, чтобы оно тоже очищалось
		newMessageIDs = append(newMessageIDs, msg.ID)
	}

	// Сохраняем ID всех новых сообщений в кеш для последующей очистки
	if len(newMessageIDs) > 0 {
		h.cache.Set(5000000+userID, newMessageIDs)
	}
}

func buildSingleBroadcastEntry(task database.BroadcastTask, page int) (string, models.InlineKeyboardMarkup) {
	text := fmt.Sprintf(`ID: %d
Текст: %s
Аудитория: %s
Дата/Время: %s
Статус: %s`,
		task.ID,
		task.Message,
		TranslateAudience(task.TargetAudience),
		FormatTimeWithTZ(task.SendAt),
		TranslateStatus(task.Status))

	var keyboard models.InlineKeyboardMarkup
	if task.Status == database.BroadcastTaskStatusPending {
		keyboard = models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{
					{Text: "✏️", CallbackData: fmt.Sprintf("broadcast_edit_%d_p%d", task.ID, page)},
					{Text: "🗑", CallbackData: fmt.Sprintf("broadcast_delete_%d_p%d", task.ID, page)},
					{Text: "🚀", CallbackData: fmt.Sprintf("broadcast_send_%d_p%d", task.ID, page)},
				},
			},
		}
	} else {
		keyboard = models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{
				{
					{Text: "🗑", CallbackData: fmt.Sprintf("broadcast_delete_%d_p%d", task.ID, page)},
				},
			},
		}
	}

	return text, keyboard
}

func buildBroadcastListPaginationMenu(page, totalPages int) (string, models.InlineKeyboardMarkup) {
	text := "<b>Отложенные рассылки</b>"
	if totalPages == 0 {
		text += "\n\nНет отложенных рассылок."
	}

	keyboard := [][]models.InlineKeyboardButton{}
	navRow := []models.InlineKeyboardButton{}
	if page > 1 {
		navRow = append(navRow, models.InlineKeyboardButton{Text: "⬅️ Предыдущие", CallbackData: fmt.Sprintf("broadcast_list_page_%d", page-1)})
	}
	if page < totalPages {
		navRow = append(navRow, models.InlineKeyboardButton{Text: "Следующие ➡️", CallbackData: fmt.Sprintf("broadcast_list_page_%d", page+1)})
	}
	if len(navRow) > 0 {
		keyboard = append(keyboard, navRow)
	}
	keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: "📝 Создать новую рассылку", CallbackData: "broadcast_create"}})
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: "⬅️ Назад", CallbackData: "admin_broadcast"},
		{Text: "🏠 Главное меню", CallbackData: "admin_menu"},
	})

	return text, models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}

// --- Генерация сообщения об успешном создании рассылки ---
func buildSuccessBroadcastCreatedMenu(audience string) (string, [][]models.InlineKeyboardButton) {
	return fmt.Sprintf("✅ Отложенная рассылка для '%s' успешно создана!", TranslateAudience(audience)), [][]models.InlineKeyboardButton{
		{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
	}
}

// --- Вспомогательная функция для форматирования времени из строки ---
func FormatTimeWithTZMustParse(timeStr string) string {
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return timeStr
	}
	return FormatTimeWithTZ(t)
}

// Вспомогательная функция для отображения статуса тарифа
func translateActiveStatus(active int) string {
	if active == 1 {
		return "✅ Активен"
	}
	return "❌ Неактивен"
}

// --- Вспомогательная функция ---
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// --- Вспомогательная функция для вывода страницы тарифов ---
func (h *Handler) showTariffsPage(ctx context.Context, b *bot.Bot, callback *models.CallbackQuery, page int) {
	msg := callback.Message.Message
	userID := callback.From.ID
	chatID := msg.Chat.ID

	h.clearTariffMessages(ctx, b, userID, chatID)

	tariffs, err := h.tariffRepository.GetAll(ctx, false)
	if err != nil {
		msg, _ := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID: chatID,
			Text:   "Ошибка получения тарифов из БД.",
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: [][]models.InlineKeyboardButton{
					{{Text: "⬅️ Назад", CallbackData: "admin_menu"}},
				},
			},
		})
		if msg != nil {
			h.cache.Set(5100000+userID, []int{msg.ID})
		}
		return
	}
	pageTariffs, totalPages := getTariffPage(tariffs, page)
	var newMessageIDs []int

	if len(pageTariffs) == 0 {
		text, keyboard := buildTariffListPaginationMenu(page, totalPages)
		msg, _ := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      chatID,
			Text:        text,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		if msg != nil {
			h.cache.Set(5100000+userID, []int{msg.ID})
		}
		return
	}

	// Для каждого тарифа отдельное сообщение
	for _, t := range pageTariffs {
		text, keyboard := buildSingleTariffEntry(t, page)
		msg, err := b.SendMessage(ctx, &bot.SendMessageParams{
			ChatID:      chatID,
			Text:        text,
			ParseMode:   models.ParseModeHTML,
			ReplyMarkup: keyboard,
		})
		if err == nil && msg != nil {
			newMessageIDs = append(newMessageIDs, msg.ID)
		}
	}

	// Меню пагинации и управления
	text, keyboard := buildTariffListPaginationMenu(page, totalPages)
	menuMsg, _ := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:      chatID,
		Text:        text,
		ParseMode:   models.ParseModeHTML,
		ReplyMarkup: keyboard,
	})
	if menuMsg != nil {
		newMessageIDs = append(newMessageIDs, menuMsg.ID)
	}

	if len(newMessageIDs) > 0 {
		h.cache.Set(5100000+userID, newMessageIDs)
	}
}

// --- Вспомогательная функция для очистки сообщений с тарифами ---
func (h *Handler) clearTariffMessages(ctx context.Context, b *bot.Bot, userID, chatID int64, exceptMsgID ...int) {
	if oldMsgIDs, ok := h.cache.Get(5100000 + userID); ok {
		if ids, ok := oldMsgIDs.([]int); ok {
			for _, id := range ids {
				skip := false
				for _, except := range exceptMsgID {
					if id == except {
						skip = true
						break
					}
				}
				if skip {
					continue
				}
				b.DeleteMessage(ctx, &bot.DeleteMessageParams{
					ChatID:    chatID,
					MessageID: id,
				})
			}
		}
		h.cache.Delete(5100000 + userID)
	}
}

// --- Генерация текста и клавиатуры для одного тарифа ---
func buildSingleTariffEntry(t database.Tariff, page int) (string, models.InlineKeyboardMarkup) {
	status := "❌ Неактивен"
	if t.Active {
		status = "✅ Активен"
	}
	text := fmt.Sprintf(`ID: %d
Название: %s
Код: %s
Цена: %d₽ / %d⭐
Статус: %s`,
		t.ID,
		t.Title,
		t.Code,
		t.PriceRUB,
		t.PriceStars,
		status)

	// Новый порядок: ✏️, 🗑, 🚀/🔄
	row := []models.InlineKeyboardButton{
		{Text: "✏️", CallbackData: fmt.Sprintf("tariff_edit_%s_p%d", t.Code, page)},
		{Text: "🗑", CallbackData: fmt.Sprintf("tariff_delete_%s_p%d", t.Code, page)},
	}
	if t.Active {
		row = append(row, models.InlineKeyboardButton{Text: "🔄", CallbackData: fmt.Sprintf("tariff_toggle_%s_p%d", t.Code, page)})
	} else {
		row = append(row, models.InlineKeyboardButton{Text: "🚀", CallbackData: fmt.Sprintf("tariff_toggle_%s_p%d", t.Code, page)})
	}
	keyboard := models.InlineKeyboardMarkup{
		InlineKeyboard: [][]models.InlineKeyboardButton{row},
	}
	return text, keyboard
}

// --- Пагинация тарифов ---
const tariffsPerPage = 3

func getTariffPage(tariffs []database.Tariff, page int) ([]database.Tariff, int) {
	if page < 1 {
		page = 1
	}
	total := len(tariffs)
	start := (page - 1) * tariffsPerPage
	if start >= total {
		return nil, (total + tariffsPerPage - 1) / tariffsPerPage
	}
	end := start + tariffsPerPage
	if end > total {
		end = total
	}
	return tariffs[start:end], (total + tariffsPerPage - 1) / tariffsPerPage
}

// --- Генерация меню пагинации тарифов ---
func buildTariffListPaginationMenu(page, totalPages int) (string, models.InlineKeyboardMarkup) {
	text := "<b>Тарифы</b>"
	if totalPages == 0 {
		text += "\n\nНет ни одного тарифа."
	}

	keyboard := [][]models.InlineKeyboardButton{}
	navRow := []models.InlineKeyboardButton{}
	if page > 1 {
		navRow = append(navRow, models.InlineKeyboardButton{Text: "⬅️ Предыдущие", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page-1)})
	}
	if page < totalPages {
		navRow = append(navRow, models.InlineKeyboardButton{Text: "Следующие ➡️", CallbackData: fmt.Sprintf("admin_tariffs_page_%d", page+1)})
	}
	if len(navRow) > 0 {
		keyboard = append(keyboard, navRow)
	}
	keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: "➕ Добавить тариф", CallbackData: "tariff_add"}})
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: "⬅️ Назад", CallbackData: "admin_menu"},
		{Text: "🏠 Главное меню", CallbackData: "admin_menu"},
	})

	return text, models.InlineKeyboardMarkup{InlineKeyboard: keyboard}
}
