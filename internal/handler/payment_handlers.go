package handler

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"log/slog"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"

	"remnawave-tg-shop-bot/internal/config"
	"remnawave-tg-shop-bot/internal/database"
)

func (h Handler) BuyCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	langCode := update.CallbackQuery.From.LanguageCode

	var priceButtons []models.InlineKeyboardButton

	// Получаем тарифы из БД
	tariffs, err := h.tariffRepository.GetAll(ctx, true)
	if err == nil && len(tariffs) > 0 {
		for _, tariff := range tariffs {
			btn := models.InlineKeyboardButton{
				Text:         fmt.Sprintf("%s (%d₽)", tariff.Title, tariff.PriceRUB),
				CallbackData: fmt.Sprintf("%s?code=%s", CallbackSell, tariff.Code),
			}
			priceButtons = append(priceButtons, btn)
		}
	}

	keyboard := [][]models.InlineKeyboardButton{}

	if len(priceButtons) == 4 {
		keyboard = append(keyboard, priceButtons[:2])
		keyboard = append(keyboard, priceButtons[2:])
	} else if len(priceButtons) > 0 {
		keyboard = append(keyboard, priceButtons)
	}

	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackStart},
	})

	_, err = b.EditMessageText(ctx, &bot.EditMessageTextParams{
		ChatID:    callback.Chat.ID,
		MessageID: callback.ID,
		ParseMode: models.ParseModeHTML,
		ReplyMarkup: models.InlineKeyboardMarkup{
			InlineKeyboard: keyboard,
		},
		Text: h.translation.GetText(langCode, "pricing_info"),
	})

	if err != nil {
		slog.Error("Error sending buy message", err)
	}
}

func (h Handler) SellCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	callbackQuery := parseCallbackData(update.CallbackQuery.Data)
	langCode := update.CallbackQuery.From.LanguageCode

	if code, ok := callbackQuery["code"]; ok && code != "" {
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			slog.Error("Ошибка поиска тарифа по коду", "code", code, "err", err)
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Chat.ID,
				MessageID: callback.ID,
				Text:      h.translation.GetText(langCode, "tariff_not_found"),
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy}},
					},
				},
			})
			return
		}
		var keyboard [][]models.InlineKeyboardButton
		if config.IsYookasaEnabled() && tariff.PriceRUB > 0 {
			cardText := fmt.Sprintf(h.translation.GetText(langCode, "card_button"), tariff.PriceRUB)
			keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: cardText, CallbackData: fmt.Sprintf("%s?code=%s&invoiceType=%s", CallbackPayment, code, database.InvoiceTypeYookasa)}})
		}
		if config.IsTelegramStarsEnabled() && tariff.PriceStars > 0 {
			starsText := fmt.Sprintf(h.translation.GetText(langCode, "stars_button"), tariff.PriceStars)
			keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: starsText, CallbackData: fmt.Sprintf("%s?code=%s&invoiceType=%s", CallbackPayment, code, database.InvoiceTypeTelegram)}})
		}
		if config.IsCryptoPayEnabled() && tariff.PriceRUB > 0 {
			cryptoText := fmt.Sprintf(h.translation.GetText(langCode, "crypto_button"), tariff.PriceRUB)
			keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: cryptoText, CallbackData: fmt.Sprintf("%s?code=%s&invoiceType=%s", CallbackPayment, code, database.InvoiceTypeCrypto)}})
		}
		if config.GetTributeWebHookUrl() != "" {
			keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: h.translation.GetText(langCode, "tribute_button"), URL: config.GetTributePaymentUrl()}})
		}
		keyboard = append(keyboard, []models.InlineKeyboardButton{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy}})
		_, err = b.EditMessageReplyMarkup(ctx, &bot.EditMessageReplyMarkupParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Ошибка отправки sell message (tariff)", err)
		}
	}
}

func (h Handler) PaymentCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	callback := update.CallbackQuery.Message.Message
	callbackQuery := parseCallbackData(update.CallbackQuery.Data)
	langCode := update.CallbackQuery.From.LanguageCode

	if code, ok := callbackQuery["code"]; ok && code != "" {
		invoiceType := database.InvoiceType(callbackQuery["invoiceType"])
		tariff, err := h.tariffRepository.GetByCode(ctx, code)
		if err != nil || tariff == nil {
			slog.Error("Ошибка поиска тарифа по коду (оплата)", "code", code, "err", err)
			_, _ = b.EditMessageText(ctx, &bot.EditMessageTextParams{
				ChatID:    callback.Chat.ID,
				MessageID: callback.ID,
				Text:      h.translation.GetText(langCode, "tariff_not_found"),
				ReplyMarkup: models.InlineKeyboardMarkup{
					InlineKeyboard: [][]models.InlineKeyboardButton{
						{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: CallbackBuy}},
					},
				},
			})
			return
		}
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
		defer cancel()
		customer, err := h.customerRepository.FindByTelegramId(ctx, callback.Chat.ID)
		if err != nil {
			slog.Error("Error finding customer", err)
			return
		}
		if customer == nil {
			slog.Error("customer not exist", "chatID", callback.Chat.ID, "error", err)
			return
		}
		ctxWithUsername := context.WithValue(ctx, "username", update.CallbackQuery.From.Username)
		paymentURL, _, err := h.paymentService.CreatePurchaseByTariff(ctxWithUsername, tariff, customer, invoiceType)
		if err != nil {
			slog.Error("Error creating payment by tariff", err)
			return
		}
		keyboard := [][]models.InlineKeyboardButton{
			{{Text: h.translation.GetText(langCode, "pay_button"), URL: paymentURL}},
			{{Text: h.translation.GetText(langCode, "back_button"), CallbackData: fmt.Sprintf("%s?code=%s", CallbackSell, code)}},
		}
		_, err = b.EditMessageReplyMarkup(ctx, &bot.EditMessageReplyMarkupParams{
			ChatID:    callback.Chat.ID,
			MessageID: callback.ID,
			ReplyMarkup: models.InlineKeyboardMarkup{
				InlineKeyboard: keyboard,
			},
		})
		if err != nil {
			slog.Error("Error updating sell message (tariff)", err)
		}
	}
}

func (h Handler) PreCheckoutCallbackHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	_, err := b.AnswerPreCheckoutQuery(ctx, &bot.AnswerPreCheckoutQueryParams{
		PreCheckoutQueryID: update.PreCheckoutQuery.ID,
		OK:                 true,
	})
	if err != nil {
		slog.Error("Error sending answer pre checkout query", err)
	}
}

func (h Handler) SuccessPaymentHandler(ctx context.Context, b *bot.Bot, update *models.Update) {
	payload := strings.Split(update.Message.SuccessfulPayment.InvoicePayload, "&")
	purchaseId, err := strconv.Atoi(payload[0])
	username := payload[1]
	if err != nil {
		slog.Error("Error parsing purchase id", err)
		return
	}

	ctxWithUsername := context.WithValue(ctx, "username", username)
	err = h.paymentService.ProcessPurchaseById(ctxWithUsername, int64(purchaseId))
	if err != nil {
		slog.Error("Error processing purchase", err)
	}
}

func parseCallbackData(data string) map[string]string {
	result := make(map[string]string)

	parts := strings.Split(data, "?")
	if len(parts) < 2 {
		return result
	}

	params := strings.Split(parts[1], "&")
	for _, param := range params {
		kv := strings.SplitN(param, "=", 2)
		if len(kv) == 2 {
			result[kv[0]] = kv[1]
		}
	}

	return result
}
