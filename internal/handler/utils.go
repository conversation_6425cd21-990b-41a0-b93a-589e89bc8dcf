package handler

import "remnawave-tg-shop-bot/internal/database"

func TranslateAudience(audience string) string {
	switch audience {
	case "all":
		return "Всем пользователям"
	case "unsubscribed":
		return "Без подписки"
	default:
		return audience
	}
}

func TranslateStatus(status database.BroadcastTaskStatus) string {
	switch status {
	case database.BroadcastTaskStatusPending:
		return "В ожидании"
	case database.BroadcastTaskStatusSent:
		return "Отправлено"
	case database.BroadcastTaskStatusCancelled:
		return "Отменено"
	default:
		return string(status)
	}
}
