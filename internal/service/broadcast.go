package service

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/translation"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// BroadcastService предоставляет бизнес-логику для управления рассылками
type BroadcastService struct {
	broadcastTaskService *BroadcastTaskService
	customerRepository   *database.CustomerRepository
	translation          *translation.Manager
}

// NewBroadcastService создает новый сервис для управления рассылками
func NewBroadcastService(
	broadcastTaskService *BroadcastTaskService,
	customerRepository *database.CustomerRepository,
	translation *translation.Manager,
) *BroadcastService {
	return &BroadcastService{
		broadcastTaskService: broadcastTaskService,
		customerRepository:   customerRepository,
		translation:          translation,
	}
}

// CreateScheduledBroadcast создает отложенную рассылку
func (s *BroadcastService) CreateScheduledBroadcast(ctx context.Context, text string, sendAt time.Time, targetAudience string) (*database.BroadcastTask, error) {
	task := &database.BroadcastTask{
		Message:        text,
		SendAt:         sendAt.UTC(),
		Status:         database.BroadcastTaskStatusPending,
		TargetAudience: targetAudience,
	}

	id, err := s.broadcastTaskService.Create(ctx, task)
	if err != nil {
		return nil, fmt.Errorf("failed to create broadcast task: %w", err)
	}

	task.ID = id
	return task, nil
}

// UpdateBroadcast обновляет существующую рассылку
func (s *BroadcastService) UpdateBroadcast(ctx context.Context, id int64, newText string, newSendAt time.Time) error {
	// Получаем существующую задачу
	task, err := s.broadcastTaskService.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get broadcast task: %w", err)
	}
	if task == nil {
		return fmt.Errorf("broadcast task not found")
	}

	// Удаляем старую задачу
	err = s.broadcastTaskService.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete old broadcast task: %w", err)
	}

	// Создаем новую задачу с обновленными данными
	updatedTask := &database.BroadcastTask{
		Message:        newText,
		SendAt:         newSendAt.UTC(),
		Status:         database.BroadcastTaskStatusPending,
		TargetAudience: task.TargetAudience,
	}

	_, err = s.broadcastTaskService.Create(ctx, updatedTask)
	if err != nil {
		return fmt.Errorf("failed to create updated broadcast task: %w", err)
	}

	return nil
}

// DeleteBroadcast удаляет рассылку
func (s *BroadcastService) DeleteBroadcast(ctx context.Context, id int64) error {
	err := s.broadcastTaskService.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete broadcast task: %w", err)
	}
	return nil
}

// GetBroadcast получает рассылку по ID
func (s *BroadcastService) GetBroadcast(ctx context.Context, id int64) (*database.BroadcastTask, error) {
	task, err := s.broadcastTaskService.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get broadcast task: %w", err)
	}
	return task, nil
}

// GetAllBroadcasts получает все рассылки
func (s *BroadcastService) GetAllBroadcasts(ctx context.Context) ([]database.BroadcastTask, error) {
	tasks, err := s.broadcastTaskService.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all broadcast tasks: %w", err)
	}
	return tasks, nil
}

// SendInstantBroadcast отправляет моментальную рассылку
func (s *BroadcastService) SendInstantBroadcast(ctx context.Context, b *bot.Bot, text string, targetAudience string) error {
	customers, err := s.getTargetCustomers(ctx, targetAudience)
	if err != nil {
		return fmt.Errorf("failed to get target customers: %w", err)
	}

	slog.Info("Starting instant broadcast", "target_audience", targetAudience, "customers_count", len(customers))

	successCount := 0
	errorCount := 0

	for _, customer := range customers {
		err := s.sendMessageToCustomer(ctx, b, customer, text)
		if err != nil {
			slog.Error("Failed to send message to customer", "customer_id", customer.TelegramID, "error", err)
			errorCount++
		} else {
			successCount++
		}
	}

	slog.Info("Instant broadcast completed", 
		"target_audience", targetAudience, 
		"success_count", successCount, 
		"error_count", errorCount)

	return nil
}

// SendScheduledBroadcast отправляет отложенную рассылку и обновляет её статус
func (s *BroadcastService) SendScheduledBroadcast(ctx context.Context, b *bot.Bot, taskID int64) error {
	task, err := s.broadcastTaskService.GetByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get broadcast task: %w", err)
	}
	if task == nil {
		return fmt.Errorf("broadcast task not found")
	}

	customers, err := s.getTargetCustomers(ctx, task.TargetAudience)
	if err != nil {
		return fmt.Errorf("failed to get target customers: %w", err)
	}

	slog.Info("Starting scheduled broadcast", "task_id", taskID, "target_audience", task.TargetAudience, "customers_count", len(customers))

	successCount := 0
	errorCount := 0

	for _, customer := range customers {
		err := s.sendMessageToCustomer(ctx, b, customer, task.Message)
		if err != nil {
			slog.Error("Failed to send message to customer", "customer_id", customer.TelegramID, "error", err)
			errorCount++
		} else {
			successCount++
		}
	}

	// Обновляем статус задачи
	err = s.broadcastTaskService.UpdateStatus(ctx, taskID, database.BroadcastTaskStatusSent)
	if err != nil {
		slog.Error("Failed to update broadcast task status", "task_id", taskID, "error", err)
	}

	slog.Info("Scheduled broadcast completed", 
		"task_id", taskID,
		"target_audience", task.TargetAudience, 
		"success_count", successCount, 
		"error_count", errorCount)

	return nil
}

// getTargetCustomers получает список клиентов для рассылки в зависимости от аудитории
func (s *BroadcastService) getTargetCustomers(ctx context.Context, targetAudience string) ([]database.Customer, error) {
	switch targetAudience {
	case "all":
		return s.customerRepository.GetAll(ctx)
	case "active":
		// Получаем клиентов с активной подпиской
		// Предполагается, что есть метод для получения активных пользователей
		return s.customerRepository.GetAll(ctx) // Заглушка - нужно реализовать GetActiveCustomers
	case "inactive":
		// Получаем клиентов без активной подписки
		return s.customerRepository.GetUsersWithoutActiveSubscription(ctx)
	default:
		return nil, fmt.Errorf("unknown target audience: %s", targetAudience)
	}
}

// sendMessageToCustomer отправляет сообщение конкретному клиенту
func (s *BroadcastService) sendMessageToCustomer(ctx context.Context, b *bot.Bot, customer database.Customer, text string) error {
	_, err := b.SendMessage(ctx, &bot.SendMessageParams{
		ChatID:    customer.TelegramID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
	})
	return err
}

// ValidateScheduleTime проверяет, что время отправки корректно
func (s *BroadcastService) ValidateScheduleTime(scheduleTime time.Time) error {
	now := time.Now()
	if scheduleTime.Before(now) {
		return fmt.Errorf("schedule time cannot be in the past")
	}
	
	// Проверяем, что время не слишком далеко в будущем (например, не более года)
	maxFutureTime := now.AddDate(1, 0, 0) // +1 год
	if scheduleTime.After(maxFutureTime) {
		return fmt.Errorf("schedule time cannot be more than 1 year in the future")
	}
	
	return nil
}

// GetBroadcastStats возвращает статистику по рассылкам
func (s *BroadcastService) GetBroadcastStats(ctx context.Context) (map[string]int, error) {
	tasks, err := s.broadcastTaskService.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get broadcast tasks: %w", err)
	}

	stats := map[string]int{
		"total":   len(tasks),
		"pending": 0,
		"sent":    0,
		"failed":  0,
	}

	for _, task := range tasks {
		switch task.Status {
		case database.BroadcastTaskStatusPending:
			stats["pending"]++
		case database.BroadcastTaskStatusSent:
			stats["sent"]++
		case database.BroadcastTaskStatusFailed:
			stats["failed"]++
		}
	}

	return stats, nil
}
