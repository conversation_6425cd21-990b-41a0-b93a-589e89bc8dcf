package service

import (
	"context"
	"remnawave-tg-shop-bot/internal/database"
	"time"

	"github.com/go-telegram/bot"
)

// BroadcastServiceInterface определяет интерфейс для сервиса управления рассылками
type BroadcastServiceInterface interface {
	// CreateScheduledBroadcast создает отложенную рассылку
	CreateScheduledBroadcast(ctx context.Context, text string, sendAt time.Time, targetAudience string) (*database.BroadcastTask, error)

	// SendPaginatedBroadcastList удален - слишком специфичен для handler

	// UpdateBroadcast обновляет существующую рассылку
	UpdateBroadcast(ctx context.Context, id int64, newText string, newSendAt time.Time) error

	// DeleteBroadcast удаляет рассылку
	DeleteBroadcast(ctx context.Context, id int64) error

	// GetBroadcast получает рассылку по ID
	GetBroadcast(ctx context.Context, id int64) (*database.BroadcastTask, error)

	// GetAllBroadcasts получает все рассылки
	GetAllBroadcasts(ctx context.Context) ([]database.BroadcastTask, error)

	// SendInstantBroadcast отправляет моментальную рассылку
	SendInstantBroadcast(ctx context.Context, b *bot.Bot, text string, targetAudience string) error

	// ValidateScheduleTime проверяет, что время отправки корректно
	ValidateScheduleTime(scheduleTime time.Time) error

	// GetBroadcastStats возвращает статистику по рассылкам
	GetBroadcastStats(ctx context.Context) (map[string]int, error)
}

// Убеждаемся, что BroadcastService реализует интерфейс BroadcastServiceInterface
var _ BroadcastServiceInterface = (*BroadcastService)(nil)
