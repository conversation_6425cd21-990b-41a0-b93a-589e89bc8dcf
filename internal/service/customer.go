package service

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
)

// CustomerService предоставляет бизнес-логику для работы с пользователями
type CustomerService struct {
	customerRepository *database.CustomerRepository
}

// NewCustomerService создает новый экземпляр CustomerService
func NewCustomerService(customerRepository *database.CustomerRepository) *CustomerService {
	return &CustomerService{
		customerRepository: customerRepository,
	}
}

// GetAllCustomers получает всех пользователей
func (cs *CustomerService) GetAllCustomers(ctx context.Context) ([]database.Customer, error) {
	customers, err := cs.customerRepository.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all customers: %w", err)
	}
	
	slog.Info("Retrieved all customers", "count", len(customers))
	return customers, nil
}

// GetUsersWithoutActiveSubscription получает пользователей без активной подписки
func (cs *CustomerService) GetUsersWithoutActiveSubscription(ctx context.Context) ([]database.Customer, error) {
	customers, err := cs.customerRepository.GetUsersWithoutActiveSubscription(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get users without active subscription: %w", err)
	}
	
	slog.Info("Retrieved users without active subscription", "count", len(customers))
	return customers, nil
}

// Убеждаемся, что CustomerService реализует интерфейс CustomerServiceInterface
var _ CustomerServiceInterface = (*CustomerService)(nil)
