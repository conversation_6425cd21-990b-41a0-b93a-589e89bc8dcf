package service

import (
	"context"
	"remnawave-tg-shop-bot/internal/database"
)

// CustomerServiceInterface определяет интерфейс для сервиса управления пользователями
type CustomerServiceInterface interface {
	// GetAllCustomers получает всех пользователей
	GetAllCustomers(ctx context.Context) ([]database.Customer, error)

	// GetUsersWithoutActiveSubscription получает пользователей без активной подписки
	GetUsersWithoutActiveSubscription(ctx context.Context) ([]database.Customer, error)
}
