package service

import (
	"context"
	"fmt"
	"log/slog"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/translation"
	"strings"
	"unicode/utf8"
)

// TariffService предоставляет бизнес-логику для управления тарифами
type TariffService struct {
	tariffRepository *database.TariffRepository
	translation      *translation.Manager
}

// NewTariffService создает новый сервис для управления тарифами
func NewTariffService(
	tariffRepository *database.TariffRepository,
	translation *translation.Manager,
) *TariffService {
	return &TariffService{
		tariffRepository: tariffRepository,
		translation:      translation,
	}
}

// CreateTariff создает новый тариф
func (s *TariffService) CreateTariff(ctx context.Context, code, title string, priceRUB, priceStars int) (*database.Tariff, error) {
	// Валидация входных данных
	if err := s.ValidateTariffData(code, title, priceRUB, priceStars); err != nil {
		return nil, err
	}

	// Проверяем, что тариф с таким кодом не существует
	existingTariff, err := s.tariffRepository.GetByCode(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing tariff: %w", err)
	}
	if existingTariff != nil {
		return nil, fmt.Errorf("tariff with code '%s' already exists", code)
	}

	tariff := &database.Tariff{
		Code:       code,
		Title:      title,
		PriceRUB:   priceRUB,
		PriceStars: priceStars,
		Active:     0, // Новые тарифы создаются неактивными
	}

	err = s.tariffRepository.Create(ctx, tariff)
	if err != nil {
		return nil, fmt.Errorf("failed to create tariff: %w", err)
	}

	slog.Info("Tariff created", "code", code, "title", title, "price_rub", priceRUB, "price_stars", priceStars)
	return tariff, nil
}

// UpdateTariff обновляет существующий тариф
func (s *TariffService) UpdateTariff(ctx context.Context, code, newTitle string, newPriceRUB, newPriceStars int) error {
	// Валидация входных данных
	if err := s.ValidateTariffUpdateData(newTitle, newPriceRUB, newPriceStars); err != nil {
		return err
	}

	// Получаем существующий тариф
	existingTariff, err := s.tariffRepository.GetByCode(ctx, code)
	if err != nil {
		return fmt.Errorf("failed to get existing tariff: %w", err)
	}
	if existingTariff == nil {
		return fmt.Errorf("tariff with code '%s' not found", code)
	}

	// Обновляем тариф
	updatedTariff := &database.Tariff{
		Code:       code,
		Title:      newTitle,
		PriceRUB:   newPriceRUB,
		PriceStars: newPriceStars,
		Active:     existingTariff.Active, // Сохраняем текущий статус активности
	}

	err = s.tariffRepository.Update(ctx, updatedTariff)
	if err != nil {
		return fmt.Errorf("failed to update tariff: %w", err)
	}

	slog.Info("Tariff updated", "code", code, "new_title", newTitle, "new_price_rub", newPriceRUB, "new_price_stars", newPriceStars)
	return nil
}

// DeleteTariff удаляет тариф по ID
func (s *TariffService) DeleteTariff(ctx context.Context, id int64) error {
	err := s.tariffRepository.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete tariff: %w", err)
	}

	slog.Info("Tariff deleted", "id", id)
	return nil
}

// GetTariff получает тариф по коду
func (s *TariffService) GetTariff(ctx context.Context, code string) (*database.Tariff, error) {
	tariff, err := s.tariffRepository.GetByCode(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get tariff: %w", err)
	}
	return tariff, nil
}

// GetAllTariffs получает все тарифы
func (s *TariffService) GetAllTariffs(ctx context.Context, includeInactive bool) ([]database.Tariff, error) {
	tariffs, err := s.tariffRepository.GetAll(ctx, includeInactive)
	if err != nil {
		return nil, fmt.Errorf("failed to get all tariffs: %w", err)
	}
	return tariffs, nil
}

// GetTariffByCode получает тариф по коду
func (s *TariffService) GetTariffByCode(ctx context.Context, code string) (*database.Tariff, error) {
	tariff, err := s.tariffRepository.GetByCode(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get tariff by code: %w", err)
	}
	return tariff, nil
}

// SetTariffActive устанавливает статус активности тарифа
func (s *TariffService) SetTariffActive(ctx context.Context, id int64, active bool) error {
	err := s.tariffRepository.SetActive(ctx, id, active)
	if err != nil {
		return fmt.Errorf("failed to set tariff active status: %w", err)
	}
	return nil
}

// GetActiveTariffs получает только активные тарифы
func (s *TariffService) GetActiveTariffs(ctx context.Context) ([]database.Tariff, error) {
	allTariffs, err := s.tariffRepository.GetAll(ctx, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get all tariffs: %w", err)
	}

	var activeTariffs []database.Tariff
	for _, tariff := range allTariffs {
		if tariff.Active {
			activeTariffs = append(activeTariffs, tariff)
		}
	}

	return activeTariffs, nil
}

// ActivateTariff активирует тариф
func (s *TariffService) ActivateTariff(ctx context.Context, code string) error {
	return s.setTariffActiveStatus(ctx, code, 1)
}

// DeactivateTariff деактивирует тариф
func (s *TariffService) DeactivateTariff(ctx context.Context, code string) error {
	return s.setTariffActiveStatus(ctx, code, 0)
}

// setTariffActiveStatus устанавливает статус активности тарифа
func (s *TariffService) setTariffActiveStatus(ctx context.Context, code string, active int) error {
	// Получаем существующий тариф
	existingTariff, err := s.tariffRepository.GetByCode(ctx, code)
	if err != nil {
		return fmt.Errorf("failed to get existing tariff: %w", err)
	}
	if existingTariff == nil {
		return fmt.Errorf("tariff with code '%s' not found", code)
	}

	// Обновляем только статус активности
	updatedTariff := &database.Tariff{
		Code:       existingTariff.Code,
		Title:      existingTariff.Title,
		PriceRUB:   existingTariff.PriceRUB,
		PriceStars: existingTariff.PriceStars,
		Active:     active == 1,
	}

	err = s.tariffRepository.Update(ctx, updatedTariff)
	if err != nil {
		return fmt.Errorf("failed to update tariff status: %w", err)
	}

	status := "deactivated"
	if active == 1 {
		status = "activated"
	}
	slog.Info("Tariff status changed", "code", code, "status", status)
	return nil
}

// ValidateTariffData валидирует данные тарифа при создании
func (s *TariffService) ValidateTariffData(code, title string, priceRUB, priceStars int) error {
	// Валидация кода
	if err := s.ValidateTariffCode(code); err != nil {
		return err
	}

	// Валидация остальных данных
	return s.ValidateTariffUpdateData(title, priceRUB, priceStars)
}

// ValidateTariffUpdateData валидирует данные тарифа при обновлении
func (s *TariffService) ValidateTariffUpdateData(title string, priceRUB, priceStars int) error {
	// Валидация названия
	if err := s.ValidateTariffTitle(title); err != nil {
		return err
	}

	// Валидация цен
	if err := s.ValidateTariffPrices(priceRUB, priceStars); err != nil {
		return err
	}

	return nil
}

// ValidateTariffCode валидирует код тарифа
func (s *TariffService) ValidateTariffCode(code string) error {
	if code == "" {
		return fmt.Errorf("tariff code cannot be empty")
	}

	if len(code) > 32 {
		return fmt.Errorf("tariff code cannot be longer than 32 characters")
	}

	// Проверяем, что код содержит только допустимые символы (латиница, цифры, подчеркивания, дефисы)
	for _, r := range code {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_' || r == '-') {
			return fmt.Errorf("tariff code can only contain latin letters, digits, underscores and hyphens")
		}
	}

	return nil
}

// ValidateTariffTitle валидирует название тарифа
func (s *TariffService) ValidateTariffTitle(title string) error {
	if title == "" {
		return fmt.Errorf("tariff title cannot be empty")
	}

	title = strings.TrimSpace(title)
	if title == "" {
		return fmt.Errorf("tariff title cannot be empty")
	}

	if utf8.RuneCountInString(title) > 64 {
		return fmt.Errorf("tariff title cannot be longer than 64 characters")
	}

	return nil
}

// ValidateTariffPrices валидирует цены тарифа
func (s *TariffService) ValidateTariffPrices(priceRUB, priceStars int) error {
	if priceRUB <= 0 {
		return fmt.Errorf("price in RUB must be positive")
	}

	if priceStars <= 0 {
		return fmt.Errorf("price in Stars must be positive")
	}

	// Проверяем разумные лимиты
	if priceRUB > 1000000 { // 1 миллион рублей
		return fmt.Errorf("price in RUB cannot exceed 1,000,000")
	}

	if priceStars > 10000 { // 10 тысяч звезд
		return fmt.Errorf("price in Stars cannot exceed 10,000")
	}

	return nil
}

// GetTariffStats возвращает статистику по тарифам
func (s *TariffService) GetTariffStats(ctx context.Context) (map[string]int, error) {
	tariffs, err := s.tariffRepository.GetAll(ctx, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get all tariffs: %w", err)
	}

	stats := map[string]int{
		"total":    len(tariffs),
		"active":   0,
		"inactive": 0,
	}

	for _, tariff := range tariffs {
		if tariff.Active {
			stats["active"]++
		} else {
			stats["inactive"]++
		}
	}

	return stats, nil
}
