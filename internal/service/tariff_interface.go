package service

import (
	"context"
	"remnawave-tg-shop-bot/internal/database"
)

// TariffServiceInterface определяет интерфейс для сервиса управления тарифами
type TariffServiceInterface interface {
	// CreateTariff создает новый тариф
	CreateTariff(ctx context.Context, code, title string, priceRUB, priceStars int) (*database.Tariff, error)
	
	// UpdateTariff обновляет существующий тариф
	UpdateTariff(ctx context.Context, code, newTitle string, newPriceRUB, newPriceStars int) error
	
	// DeleteTariff удаляет тариф
	DeleteTariff(ctx context.Context, code string) error
	
	// GetTariff получает тариф по коду
	GetTariff(ctx context.Context, code string) (*database.Tariff, error)
	
	// GetAllTariffs получает все тарифы
	GetAllTariffs(ctx context.Context) ([]database.Tariff, error)
	
	// GetActiveTariffs получает только активные тарифы
	GetActiveTariffs(ctx context.Context) ([]database.Tariff, error)
	
	// ActivateTariff активирует тариф
	ActivateTariff(ctx context.Context, code string) error
	
	// DeactivateTariff деактивирует тариф
	DeactivateTariff(ctx context.Context, code string) error
	
	// ValidateTariffData валидирует данные тарифа при создании
	ValidateTariffData(code, title string, priceRUB, priceStars int) error
	
	// ValidateTariffUpdateData валидирует данные тарифа при обновлении
	ValidateTariffUpdateData(title string, priceRUB, priceStars int) error
	
	// ValidateTariffCode валидирует код тарифа
	ValidateTariffCode(code string) error
	
	// ValidateTariffTitle валидирует название тарифа
	ValidateTariffTitle(title string) error
	
	// ValidateTariffPrices валидирует цены тарифа
	ValidateTariffPrices(priceRUB, priceStars int) error
	
	// GetTariffStats возвращает статистику по тарифам
	GetTariffStats(ctx context.Context) (map[string]int, error)
}

// Убеждаемся, что TariffService реализует интерфейс TariffServiceInterface
var _ TariffServiceInterface = (*TariffService)(nil)
