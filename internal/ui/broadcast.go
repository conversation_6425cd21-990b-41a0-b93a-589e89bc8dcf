package ui

import (
	"fmt"
	"remnawave-tg-shop-bot/internal/database"
	"strconv"

	"github.com/go-telegram/bot/models"
)

// BuildBroadcastListPaginationMenu генерирует меню пагинации рассылок
func (u *UIBuilder) BuildBroadcastListPaginationMenu(page, totalPages int) PaginatedMenuResponse {
	text := "<b>Отложенные рассылки</b>"
	if totalPages == 0 {
		text += "\n\nНет отложенных рассылок."
	}

	keyboard := [][]models.InlineKeyboardButton{}

	// Кнопки навигации
	if totalPages > 1 {
		navRow := u.BuildPaginationButtons(page, totalPages, "broadcast_list_page_")
		keyboard = append(keyboard, navRow)
	}

	// Кнопки управления
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: "➕ Создать рассылку", CallbackData: "broadcast_create"},
	})
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		u.BackButton("admin_broadcast"),
	})

	return PaginatedMenuResponse{
		Text:     text,
		Keyboard: models.InlineKeyboardMarkup{InlineKeyboard: keyboard},
	}
}

// BuildSingleBroadcastEntry генерирует запись для одной рассылки
func (u *UIBuilder) BuildSingleBroadcastEntry(task *database.BroadcastTask, page int) MenuResponse {
	text := fmt.Sprintf(
		"<b>Рассылка #%d</b>\n\n<b>Текст:</b> %s\n<b>Время отправки:</b> %s\n<b>Аудитория:</b> %s",
		task.ID,
		task.Message,
		u.FormatTimeWithTZ(task.SendAt),
		u.TranslateAudience(task.TargetAudience),
	)

	keyboard := [][]models.InlineKeyboardButton{
		{
			{Text: "✏️ Редактировать", CallbackData: fmt.Sprintf("broadcast_edit_%d_p%d", task.ID, page)},
			{Text: "🗑️ Удалить", CallbackData: fmt.Sprintf("broadcast_delete_%d_p%d", task.ID, page)},
		},
		{
			{Text: "🚀 Отправить сейчас", CallbackData: fmt.Sprintf("broadcast_send_%d_p%d", task.ID, page)},
		},
		{
			u.BackButton(fmt.Sprintf("broadcast_list_page_%d", page)),
		},
	}

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildBroadcastDeleteConfirmMenu генерирует меню подтверждения удаления рассылки
func (u *UIBuilder) BuildBroadcastDeleteConfirmMenu(task *database.BroadcastTask, page int) MenuResponse {
	text := fmt.Sprintf(
		"<b>Подтверждение удаления</b>\n\n<b>Рассылка #%d</b>\n<b>Текст:</b> %s\n<b>Время отправки:</b> %s\n\n❗ Это действие нельзя отменить!",
		task.ID,
		task.Message,
		u.FormatTimeWithTZ(task.SendAt),
	)

	keyboard := [][]models.InlineKeyboardButton{
		{
			{Text: "✅ Да, удалить", CallbackData: fmt.Sprintf("broadcast_delete_confirm_%d_p%d", task.ID, page)},
			{Text: "❌ Отмена", CallbackData: fmt.Sprintf("broadcast_revert_%d_p%d", task.ID, page)},
		},
	}

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildBroadcastCreateConfirmMenu генерирует меню подтверждения создания рассылки
func (u *UIBuilder) BuildBroadcastCreateConfirmMenu(text, timeStr string) MenuResponse {
	menuText := fmt.Sprintf(
		"<b>Подтверждение создания рассылки:</b>\n\n<b>Текст:</b> %s\n<b>Время отправки:</b> %s\n\nВыберите аудиторию:",
		text,
		timeStr,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "👥 Всем пользователям", CallbackData: "broadcast_create_confirm_all"}},
		{{Text: "✅ Активным пользователям", CallbackData: "broadcast_create_confirm_active"}},
		{{Text: "❌ Неактивным пользователям", CallbackData: "broadcast_create_confirm_inactive"}},
		{u.CancelButton("broadcast_create_cancel")},
	}

	return MenuResponse{
		Text:     menuText,
		Keyboard: keyboard,
	}
}

// BuildInstantBroadcastConfirmMenu генерирует меню подтверждения моментальной рассылки
func (u *UIBuilder) BuildInstantBroadcastConfirmMenu(text string) MenuResponse {
	menuText := fmt.Sprintf(
		"<b>Подтверждение моментальной рассылки:</b>\n\n<b>Текст:</b> %s\n\nВыберите аудиторию:",
		text,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "👥 Всем пользователям", CallbackData: "instant_broadcast_confirm_all"}},
		{{Text: "✅ Активным пользователям", CallbackData: "instant_broadcast_confirm_active"}},
		{{Text: "❌ Неактивным пользователям", CallbackData: "instant_broadcast_confirm_inactive"}},
		{u.CancelButton("admin_broadcast")},
	}

	return MenuResponse{
		Text:     menuText,
		Keyboard: keyboard,
	}
}

// BuildBroadcastEditTimeMenu генерирует меню редактирования времени рассылки
func (u *UIBuilder) BuildBroadcastEditTimeMenu(task *database.BroadcastTask, newText string) MenuResponse {
	text := fmt.Sprintf(
		"<b>Редактирование времени рассылки:</b>\n\n<b>Новый текст:</b> %s\n\nВведите новое время отправки в формате:\n<code>ДД.ММ.ГГГГ ЧЧ:ММ</code>\n\nПример: <code>25.12.2024 15:30</code>",
		newText,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{u.CancelButton("broadcast_list")},
	}

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildBroadcastEditConfirmMenu генерирует меню подтверждения редактирования рассылки
func (u *UIBuilder) BuildBroadcastEditConfirmMenu(task *database.BroadcastTask, newText, newTimeStr string, page int) MenuResponse {
	text := fmt.Sprintf(
		"<b>Подтверждение изменений:</b>\n\n<b>Старый текст:</b> %s\n<b>Новый текст:</b> %s\n\n<b>Старое время:</b> %s\n<b>Новое время:</b> %s",
		task.Message,
		newText,
		u.FormatTimeWithTZ(task.SendAt),
		newTimeStr,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "✅ Сохранить изменения", CallbackData: fmt.Sprintf("broadcast_edit_confirm_%d_p%d", task.ID, page)}},
		{u.CancelButton("broadcast_list")},
	}

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildBroadcastSuccessMenu генерирует меню успешного выполнения операции с рассылкой
func (u *UIBuilder) BuildBroadcastSuccessMenu(message string) MenuResponse {
	return MenuResponse{
		Text: fmt.Sprintf("✅ %s", message),
		Keyboard: [][]models.InlineKeyboardButton{
			{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
		},
	}
}

// ParseBroadcastCallback парсит callback данные для рассылок
func ParseBroadcastCallback(callbackData string) (action string, id int64, page int) {
	// Примеры: "broadcast_edit_123_p2", "broadcast_delete_456_p1"
	
	// Простой парсинг для демонстрации
	// В реальном проекте лучше использовать более надежный парсер
	
	if len(callbackData) < 10 {
		return "", 0, 1
	}
	
	// Найти последнее вхождение "_p"
	pageIndex := -1
	for i := len(callbackData) - 3; i >= 0; i-- {
		if callbackData[i:i+2] == "_p" {
			pageIndex = i
			break
		}
	}
	
	page = 1
	if pageIndex != -1 {
		if pageNum, err := strconv.Atoi(callbackData[pageIndex+2:]); err == nil {
			page = pageNum
		}
		callbackData = callbackData[:pageIndex]
	}
	
	// Найти последнее вхождение "_" для извлечения ID
	lastUnderscore := -1
	for i := len(callbackData) - 1; i >= 0; i-- {
		if callbackData[i] == '_' {
			lastUnderscore = i
			break
		}
	}
	
	if lastUnderscore != -1 {
		action = callbackData[:lastUnderscore]
		if idNum, err := strconv.ParseInt(callbackData[lastUnderscore+1:], 10, 64); err == nil {
			id = idNum
		}
	}
	
	return action, id, page
}
