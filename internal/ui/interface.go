package ui

import (
	"remnawave-tg-shop-bot/internal/database"
	"time"

	"github.com/go-telegram/bot/models"
)

// UI определяет интерфейс для генерации пользовательского интерфейса
type UI interface {
	// --- Стандартные кнопки ---
	BackButton(callbackData string) models.InlineKeyboardButton
	CancelButton(callbackData string) models.InlineKeyboardButton
	ConfirmButton(callbackData string) models.InlineKeyboardButton

	// --- Админ-меню ---
	BuildAdminMenu() MenuResponse

	// --- Меню рассылок ---
	BuildBroadcastMenu() MenuResponse
	BuildInstantBroadcastMenu() MenuResponse
	BuildCreateBroadcastMenu() MenuResponse
	BuildEditBroadcastMenu(task *database.BroadcastTask, page int) MenuResponse
	BuildSuccessBroadcastCreatedMenu(audience string) MenuResponse
	BuildBroadcastListPaginationMenu(page, totalPages int) PaginatedMenuResponse
	BuildSingleBroadcastEntry(task *database.BroadcastTask, page int) MenuResponse
	BuildBroadcastDeleteConfirmMenu(task *database.BroadcastTask, page int) MenuResponse
	BuildBroadcastCreateConfirmMenu(text, timeStr string) MenuResponse
	BuildInstantBroadcastConfirmMenu(text string) MenuResponse
	BuildBroadcastEditTimeMenu(task *database.BroadcastTask, newText string) MenuResponse
	BuildBroadcastEditConfirmMenu(task *database.BroadcastTask, newText, newTimeStr string, page int) MenuResponse
	BuildBroadcastSuccessMenu(message string) MenuResponse

	// --- Меню тарифов ---
	BuildTariffListPaginationMenu(page, totalPages int) PaginatedMenuResponse
	BuildSingleTariffEntry(tariff *database.Tariff, page int) MenuResponse
	BuildTariffCreateMenu() MenuResponse
	BuildTariffTitleMenu() MenuResponse
	BuildTariffPriceRUBMenu() MenuResponse
	BuildTariffPriceStarsMenu() MenuResponse
	BuildTariffConfirmMenu(code, title string, priceRUB, priceStars int) MenuResponse
	BuildTariffEditConfirmMenu(tariff *database.Tariff, newTitle string, newPriceRUB, newPriceStars int) MenuResponse
	BuildTariffDeleteConfirmMenu(tariff *database.Tariff, page int) MenuResponse
	BuildTariffSuccessMenu(message string) MenuResponse

	// --- Утилиты ---
	TranslateAudience(audience string) string
	TranslateActiveStatus(active int) string
	FormatTimeWithTZ(t time.Time) string
	BuildPaginationButtons(currentPage, totalPages int, callbackPrefix string) []models.InlineKeyboardButton
}

// Убеждаемся, что UIBuilder реализует интерфейс UI
var _ UI = (*UIBuilder)(nil)
