package ui

import (
	"fmt"
	"remnawave-tg-shop-bot/internal/database"
	"strconv"

	"github.com/go-telegram/bot/models"
)

// BuildTariffListPaginationMenu генерирует меню пагинации тарифов
func (u *UIBuilder) BuildTariffListPaginationMenu(page, totalPages int) PaginatedMenuResponse {
	text := "<b>Тарифы</b>"
	if totalPages == 0 {
		text += "\n\nНет ни одного тарифа."
	}

	keyboard := [][]models.InlineKeyboardButton{}

	// Кнопки навигации
	if totalPages > 1 {
		navRow := u.BuildPaginationButtons(page, totalPages, "admin_tariffs_page_")
		keyboard = append(keyboard, navRow)
	}

	// Кнопки управления
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: "➕ Добавить тариф", CallbackData: "tariff_add"},
	})
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		u.BackButton("admin_menu"),
	})

	return PaginatedMenuResponse{
		Text:     text,
		Keyboard: models.InlineKeyboardMarkup{InlineKeyboard: keyboard},
	}
}

// BuildSingleTariffEntry генерирует запись для одного тарифа
func (u *UIBuilder) BuildSingleTariffEntry(tariff *database.Tariff, page int) MenuResponse {
	statusText := u.TranslateActiveStatus(tariff.Active)
	
	text := fmt.Sprintf(
		"<b>Тариф: %s</b>\n\n<b>Код:</b> %s\n<b>Цена (RUB):</b> %d ₽\n<b>Цена (Stars):</b> %d ⭐\n<b>Статус:</b> %s",
		tariff.Title,
		tariff.Code,
		tariff.PriceRUB,
		tariff.PriceStars,
		statusText,
	)

	keyboard := [][]models.InlineKeyboardButton{}

	// Кнопки управления тарифом
	actionRow := []models.InlineKeyboardButton{
		{Text: "✏️ Редактировать", CallbackData: fmt.Sprintf("tariff_edit_%s_p%d", tariff.Code, page)},
		{Text: "🗑️ Удалить", CallbackData: fmt.Sprintf("tariff_delete_%s_p%d", tariff.Code, page)},
	}
	keyboard = append(keyboard, actionRow)

	// Кнопка активации/деактивации
	var toggleText, toggleCallback string
	if tariff.Active == 1 {
		toggleText = "❌ Деактивировать"
		toggleCallback = fmt.Sprintf("tariff_deactivate_%s_p%d", tariff.Code, page)
	} else {
		toggleText = "✅ Активировать"
		toggleCallback = fmt.Sprintf("tariff_activate_%s_p%d", tariff.Code, page)
	}
	
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		{Text: toggleText, CallbackData: toggleCallback},
	})

	// Кнопка "Назад"
	keyboard = append(keyboard, []models.InlineKeyboardButton{
		u.BackButton(fmt.Sprintf("admin_tariffs_page_%d", page)),
	})

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildTariffCreateMenu генерирует меню создания тарифа
func (u *UIBuilder) BuildTariffCreateMenu() MenuResponse {
	return MenuResponse{
		Text: "Введите <b>код тарифа</b> (латиница, цифры, например: 1m, 3m, basic):",
		Keyboard: [][]models.InlineKeyboardButton{
			{u.CancelButton("admin_tariffs")},
		},
	}
}

// BuildTariffTitleMenu генерирует меню ввода названия тарифа
func (u *UIBuilder) BuildTariffTitleMenu() MenuResponse {
	return MenuResponse{
		Text: "Введите <b>название тарифа</b> (до 64 символов):",
		Keyboard: [][]models.InlineKeyboardButton{
			{u.CancelButton("admin_tariffs")},
		},
	}
}

// BuildTariffPriceRUBMenu генерирует меню ввода цены в рублях
func (u *UIBuilder) BuildTariffPriceRUBMenu() MenuResponse {
	return MenuResponse{
		Text: "Введите <b>цену в рублях</b> (целое положительное число):",
		Keyboard: [][]models.InlineKeyboardButton{
			{u.CancelButton("admin_tariffs")},
		},
	}
}

// BuildTariffPriceStarsMenu генерирует меню ввода цены в звёздах
func (u *UIBuilder) BuildTariffPriceStarsMenu() MenuResponse {
	return MenuResponse{
		Text: "Введите <b>цену в звёздах Telegram</b> (целое положительное число):",
		Keyboard: [][]models.InlineKeyboardButton{
			{u.CancelButton("admin_tariffs")},
		},
	}
}

// BuildTariffConfirmMenu генерирует меню подтверждения создания тарифа
func (u *UIBuilder) BuildTariffConfirmMenu(code, title string, priceRUB, priceStars int) MenuResponse {
	text := fmt.Sprintf(
		"<b>Подтверждение создания тарифа:</b>\n\n<b>Код:</b> %s\n<b>Название:</b> %s\n<b>Цена (RUB):</b> %d ₽\n<b>Цена (Stars):</b> %d ⭐\n\n<i>Тариф будет создан неактивным</i>",
		code,
		title,
		priceRUB,
		priceStars,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{u.ConfirmButton("tariff_create")},
		{u.CancelButton("admin_tariffs")},
	}

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildTariffEditConfirmMenu генерирует меню подтверждения редактирования тарифа
func (u *UIBuilder) BuildTariffEditConfirmMenu(tariff *database.Tariff, newTitle string, newPriceRUB, newPriceStars int) MenuResponse {
	text := fmt.Sprintf(
		"<b>Подтверждение изменений тарифа:</b>\n\n<b>Код:</b> %s\n\n<b>Старое название:</b> %s\n<b>Новое название:</b> %s\n\n<b>Старая цена (RUB):</b> %d ₽\n<b>Новая цена (RUB):</b> %d ₽\n\n<b>Старая цена (Stars):</b> %d ⭐\n<b>Новая цена (Stars):</b> %d ⭐",
		tariff.Code,
		tariff.Title,
		newTitle,
		tariff.PriceRUB,
		newPriceRUB,
		tariff.PriceStars,
		newPriceStars,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "✅ Сохранить изменения", CallbackData: fmt.Sprintf("tariff_edit_confirm_%s", tariff.Code)}},
		{u.CancelButton("admin_tariffs")},
	}

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildTariffDeleteConfirmMenu генерирует меню подтверждения удаления тарифа
func (u *UIBuilder) BuildTariffDeleteConfirmMenu(tariff *database.Tariff, page int) MenuResponse {
	text := fmt.Sprintf(
		"<b>Подтверждение удаления тарифа:</b>\n\n<b>Код:</b> %s\n<b>Название:</b> %s\n<b>Цена (RUB):</b> %d ₽\n<b>Цена (Stars):</b> %d ⭐\n\n❗ Это действие нельзя отменить!",
		tariff.Code,
		tariff.Title,
		tariff.PriceRUB,
		tariff.PriceStars,
	)

	keyboard := [][]models.InlineKeyboardButton{
		{{Text: "✅ Да, удалить", CallbackData: fmt.Sprintf("tariff_delete_confirm_%s_p%d", tariff.Code, page)}},
		{u.CancelButton(fmt.Sprintf("admin_tariffs_page_%d", page))},
	}

	return MenuResponse{
		Text:     text,
		Keyboard: keyboard,
	}
}

// BuildTariffSuccessMenu генерирует меню успешного выполнения операции с тарифом
func (u *UIBuilder) BuildTariffSuccessMenu(message string) MenuResponse {
	return MenuResponse{
		Text: fmt.Sprintf("✅ %s", message),
		Keyboard: [][]models.InlineKeyboardButton{
			{{Text: "⬅️ К тарифам", CallbackData: "admin_tariffs"}},
		},
	}
}

// TranslateActiveStatus переводит статус активности тарифа
func (u *UIBuilder) TranslateActiveStatus(active int) string {
	if active == 1 {
		return "✅ Активен"
	}
	return "❌ Неактивен"
}

// ParseTariffCallback парсит callback данные для тарифов
func ParseTariffCallback(callbackData string) (action string, code string, page int) {
	// Примеры: "tariff_edit_1m_p2", "tariff_delete_basic_p1"
	
	page = 1
	
	// Найти последнее вхождение "_p"
	pageIndex := -1
	for i := len(callbackData) - 3; i >= 0; i-- {
		if i+2 < len(callbackData) && callbackData[i:i+2] == "_p" {
			pageIndex = i
			break
		}
	}
	
	if pageIndex != -1 {
		if pageNum, err := strconv.Atoi(callbackData[pageIndex+2:]); err == nil {
			page = pageNum
		}
		callbackData = callbackData[:pageIndex]
	}
	
	// Найти последнее вхождение "_" для извлечения кода
	lastUnderscore := -1
	for i := len(callbackData) - 1; i >= 0; i-- {
		if callbackData[i] == '_' {
			lastUnderscore = i
			break
		}
	}
	
	if lastUnderscore != -1 {
		action = callbackData[:lastUnderscore]
		code = callbackData[lastUnderscore+1:]
	}
	
	return action, code, page
}

// BoolToInt конвертирует bool в int (для совместимости с базой данных)
func BoolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}
