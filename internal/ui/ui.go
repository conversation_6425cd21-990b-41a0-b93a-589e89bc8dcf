package ui

import (
	"fmt"
	"remnawave-tg-shop-bot/internal/database"
	"remnawave-tg-shop-bot/internal/translation"
	"time"

	"github.com/go-telegram/bot/models"
)

// UIBuilder отвечает за генерацию пользовательского интерфейса
type UIBuilder struct {
	translation *translation.Manager
}

// NewUIBuilder создает новый экземпляр UIBuilder
func NewUIBuilder(translation *translation.Manager) *UIBuilder {
	return &UIBuilder{
		translation: translation,
	}
}

// MenuResponse представляет ответ с текстом и клавиатурой
type MenuResponse struct {
	Text     string
	Keyboard [][]models.InlineKeyboardButton
}

// PaginatedMenuResponse представляет ответ с пагинацией
type PaginatedMenuResponse struct {
	Text     string
	Keyboard models.InlineKeyboardMarkup
}

// --- Стандартные кнопки ---

// BackButton создает кнопку "Назад"
func (u *UIBuilder) BackButton(callbackData string) models.InlineKeyboardButton {
	return models.InlineKeyboardButton{
		Text:         "⬅️ Назад",
		CallbackData: callbackData,
	}
}

// CancelButton создает кнопку "Отмена"
func (u *UIBuilder) CancelButton(callbackData string) models.InlineKeyboardButton {
	return models.InlineKeyboardButton{
		Text:         "❌ Отмена",
		CallbackData: callbackData,
	}
}

// ConfirmButton создает кнопку "Подтвердить"
func (u *UIBuilder) ConfirmButton(callbackData string) models.InlineKeyboardButton {
	return models.InlineKeyboardButton{
		Text:         "✅ Подтвердить",
		CallbackData: callbackData,
	}
}

// --- Админ-меню ---

// BuildAdminMenu генерирует главное админ-меню
func (u *UIBuilder) BuildAdminMenu() MenuResponse {
	return MenuResponse{
		Text: "<b>Админ-меню:</b>",
		Keyboard: [][]models.InlineKeyboardButton{
			{{Text: "🔄 Синхронизация", CallbackData: "admin_sync"}},
			{{Text: "📢 Рассылка", CallbackData: "admin_broadcast"}},
			{{Text: "💸 Тарифы", CallbackData: "admin_tariffs"}},
		},
	}
}

// --- Меню рассылок ---

// BuildBroadcastMenu генерирует подменю рассылок
func (u *UIBuilder) BuildBroadcastMenu() MenuResponse {
	return MenuResponse{
		Text: "<b>Меню рассылок:</b>",
		Keyboard: [][]models.InlineKeyboardButton{
			{{Text: "⚡ Моментальная рассылка", CallbackData: "instant_broadcast"}},
			{{Text: "🕒 Отложенные рассылки", CallbackData: "broadcast_list"}},
			{u.BackButton("admin_menu")},
		},
	}
}

// BuildInstantBroadcastMenu генерирует меню моментальной рассылки
func (u *UIBuilder) BuildInstantBroadcastMenu() MenuResponse {
	return MenuResponse{
		Text: "<b>Моментальная рассылка:</b>\nВведите текст рассылки (будет отправлен сразу после подтверждения):",
		Keyboard: [][]models.InlineKeyboardButton{
			{u.CancelButton("admin_broadcast")},
		},
	}
}

// BuildCreateBroadcastMenu генерирует меню создания рассылки
func (u *UIBuilder) BuildCreateBroadcastMenu() MenuResponse {
	return MenuResponse{
		Text: "<b>Создание новой рассылки:</b>\nВведите текст рассылки:",
		Keyboard: [][]models.InlineKeyboardButton{
			{u.CancelButton("broadcast_create_cancel")},
		},
	}
}

// BuildEditBroadcastMenu генерирует меню редактирования рассылки
func (u *UIBuilder) BuildEditBroadcastMenu(task *database.BroadcastTask, page int) MenuResponse {
	text := fmt.Sprintf("<b>Редактирование рассылки:</b>\nТекущий текст: %s\nВведите новый текст рассылки (можно ввести любой текст, даже если он похож на дату/время):", task.Message)
	return MenuResponse{
		Text: text,
		Keyboard: [][]models.InlineKeyboardButton{
			{u.CancelButton("broadcast_list")},
		},
	}
}

// BuildSuccessBroadcastCreatedMenu генерирует сообщение об успешном создании рассылки
func (u *UIBuilder) BuildSuccessBroadcastCreatedMenu(audience string) MenuResponse {
	return MenuResponse{
		Text: fmt.Sprintf("✅ Отложенная рассылка для '%s' успешно создана!", u.TranslateAudience(audience)),
		Keyboard: [][]models.InlineKeyboardButton{
			{{Text: "⬅️ В меню рассылок", CallbackData: "admin_broadcast"}},
		},
	}
}

// --- Утилиты для рассылок ---

// TranslateAudience переводит аудиторию на русский язык
func (u *UIBuilder) TranslateAudience(audience string) string {
	switch audience {
	case "all":
		return "всех пользователей"
	case "active":
		return "активных пользователей"
	case "inactive":
		return "неактивных пользователей"
	default:
		return audience
	}
}

// FormatTimeWithTZ форматирует время с часовым поясом
func (u *UIBuilder) FormatTimeWithTZ(t time.Time) string {
	// Получаем часовой пояс из конфигурации
	loc, err := time.LoadLocation("Europe/Moscow") // Можно вынести в конфиг
	if err != nil {
		loc = time.UTC
	}
	tInLoc := t.In(loc)
	_, offset := tInLoc.Zone()
	hours := offset / 3600
	var sign string
	if hours >= 0 {
		sign = "+"
	} else {
		sign = "-"
	}
	return tInLoc.Format("02.01.2006 15:04") +
		fmt.Sprintf(" (UTC%s%02d:%02d)", sign, abs(hours), abs((offset%3600)/60))
}

// abs возвращает абсолютное значение числа
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// --- Пагинация ---

// BuildPaginationButtons создает кнопки пагинации
func (u *UIBuilder) BuildPaginationButtons(currentPage, totalPages int, callbackPrefix string) []models.InlineKeyboardButton {
	var buttons []models.InlineKeyboardButton

	// Кнопка "Предыдущая"
	if currentPage > 1 {
		buttons = append(buttons, models.InlineKeyboardButton{
			Text:         "⬅️",
			CallbackData: fmt.Sprintf("%s%d", callbackPrefix, currentPage-1),
		})
	}

	// Информация о странице
	buttons = append(buttons, models.InlineKeyboardButton{
		Text:         fmt.Sprintf("%d/%d", currentPage, totalPages),
		CallbackData: "noop", // Неактивная кнопка
	})

	// Кнопка "Следующая"
	if currentPage < totalPages {
		buttons = append(buttons, models.InlineKeyboardButton{
			Text:         "➡️",
			CallbackData: fmt.Sprintf("%s%d", callbackPrefix, currentPage+1),
		})
	}

	return buttons
}

// --- Константы пагинации ---
const (
	BroadcastsPerPage = 3
	TariffsPerPage    = 3
)

// GetBroadcastPage возвращает страницу рассылок
func GetBroadcastPage(tasks []database.BroadcastTask, page int) ([]database.BroadcastTask, int) {
	if page < 1 {
		page = 1
	}
	total := len(tasks)
	start := (page - 1) * BroadcastsPerPage
	if start >= total {
		return nil, (total + BroadcastsPerPage - 1) / BroadcastsPerPage
	}
	end := start + BroadcastsPerPage
	if end > total {
		end = total
	}
	return tasks[start:end], (total + BroadcastsPerPage - 1) / BroadcastsPerPage
}

// GetTariffPage возвращает страницу тарифов
func GetTariffPage(tariffs []database.Tariff, page int) ([]database.Tariff, int) {
	if page < 1 {
		page = 1
	}
	total := len(tariffs)
	start := (page - 1) * TariffsPerPage
	if start >= total {
		return nil, (total + TariffsPerPage - 1) / TariffsPerPage
	}
	end := start + TariffsPerPage
	if end > total {
		end = total
	}
	return tariffs[start:end], (total + TariffsPerPage - 1) / TariffsPerPage
}
