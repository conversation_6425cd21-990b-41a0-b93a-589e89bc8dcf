package utils

// GeneralUtils предоставляет общие утилиты
type GeneralUtils struct{}

// NewGeneralUtils создает новый экземпляр GeneralUtils
func NewGeneralUtils() *GeneralUtils {
	return &GeneralUtils{}
}

// BoolToInt преобразует bool в int
func (gu *GeneralUtils) BoolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// TranslateActiveStatus переводит статус активности тарифа
func (gu *GeneralUtils) TranslateActiveStatus(active int) string {
	if active == 1 {
		return "✅ Активен"
	}
	return "❌ Неактивен"
}

// GetBroadcastPage возвращает страницу рассылок для пагинации
func (gu *GeneralUtils) GetBroadcastPage(totalItems, page, itemsPerPage int) (start, end int, totalPages int) {
	if page < 1 {
		page = 1
	}
	start = (page - 1) * itemsPerPage
	if start >= totalItems {
		return start, start, (totalItems + itemsPerPage - 1) / itemsPerPage
	}
	end = start + itemsPerPage
	if end > totalItems {
		end = totalItems
	}
	totalPages = (totalItems + itemsPerPage - 1) / itemsPerPage
	return start, end, totalPages
}

// GetTariffPage возвращает страницу тарифов для пагинации
func (gu *GeneralUtils) GetTariffPage(totalItems, page, itemsPerPage int) (start, end int, totalPages int) {
	return gu.GetBroadcastPage(totalItems, page, itemsPerPage) // Та же логика
}
