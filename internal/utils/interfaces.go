package utils

import (
	"context"
	"time"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// MessageUtilsInterface определяет интерфейс для работы с сообщениями
type MessageUtilsInterface interface {
	SendMessage(ctx context.Context, b *bot.Bot, params *bot.SendMessageParams) (*models.Message, error)
	EditMessage(ctx context.Context, b *bot.Bot, params *bot.EditMessageTextParams) (*models.Message, error)
	DeleteMessage(ctx context.Context, b *bot.Bot, chatID int64, messageID int) error
	SendAndSaveMessage(ctx context.Context, b *bot.Bot, params *bot.SendMessageParams, saveFunc func(int)) (*models.Message, error)
	EditAndSaveMessage(ctx context.Context, b *bot.Bot, params *bot.EditMessageTextParams, saveFunc func(int)) (*models.Message, error)
	SendHTMLMessage(ctx context.Context, b *bot.Bot, chatID int64, text string, keyboard *models.InlineKeyboardMarkup) (*models.Message, error)
	EditHTMLMessage(ctx context.Context, b *bot.Bot, chatID int64, messageID int, text string, keyboard *models.InlineKeyboardMarkup) (*models.Message, error)
	SendMenuMessage(ctx context.Context, b *bot.Bot, chatID int64, text string, keyboard [][]models.InlineKeyboardButton) (*models.Message, error)
	EditMenuMessage(ctx context.Context, b *bot.Bot, chatID int64, messageID int, text string, keyboard [][]models.InlineKeyboardButton) (*models.Message, error)
	DeleteMessageSafely(ctx context.Context, b *bot.Bot, chatID int64, messageID int)
	DeleteMultipleMessages(ctx context.Context, b *bot.Bot, chatID int64, messageIDs []int)
	AnswerCallbackQuery(ctx context.Context, b *bot.Bot, callbackQueryID string, text string, showAlert bool) error
	SendSuccessMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error)
	SendErrorMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error)
	SendWarningMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error)
	SendInfoMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error)
}

// ValidationUtilsInterface определяет интерфейс для валидации данных
type ValidationUtilsInterface interface {
	ValidateNotEmpty(value, fieldName string) error
	ValidateLength(value, fieldName string, minLength, maxLength int) error
	ValidatePositiveInt(value int, fieldName string) error
	ValidateIntRange(value int, fieldName string, min, max int) error
	ValidateRegex(value, fieldName, pattern, errorMessage string) error
	ValidateTariffCode(code string) error
	ValidateTariffTitle(title string) error
	ValidateTariffPrice(price int, currency string) error
	ValidateBroadcastText(text string) error
	ValidateDateTime(dateTimeStr, timezone string) (time.Time, error)
	ValidateUserID(userID int64) error
	ValidateMessageID(messageID int) error
	ValidateCallbackData(data string) error
	ParsePositiveInt(s, fieldName string) (int, error)
	ValidateAudience(audience string) error
	ValidatePageNumber(page int) error
	SanitizeString(s string) string
	ValidateAndSanitizeString(value, fieldName string, minLength, maxLength int) (string, error)
}

// TimeUtilsInterface определяет интерфейс для работы с временем
type TimeUtilsInterface interface {
	GetLocation() *time.Location
	Now() time.Time
	FormatDateTime(tm time.Time) string
	FormatDateTimeWithTZ(tm time.Time) string
	ParseDateTime(dateTimeStr string) (time.Time, error)
	IsInPast(tm time.Time) bool
	IsInFuture(tm time.Time) bool
	AddDuration(tm time.Time, duration time.Duration) time.Time
	DurationUntil(tm time.Time) time.Duration
	FormatDuration(d time.Duration) string
	GetTimeUntilString(tm time.Time) string
	IsToday(tm time.Time) bool
	IsTomorrow(tm time.Time) bool
	IsYesterday(tm time.Time) bool
	FormatRelativeDate(tm time.Time) string
	StartOfDay(tm time.Time) time.Time
	EndOfDay(tm time.Time) time.Time
	GetWeekday(tm time.Time) string
	GetMonth(tm time.Time) string
	FormatFullDate(tm time.Time) string
	ValidateScheduleTime(scheduleTime time.Time) error
}

// UtilsContainer объединяет все утилиты
type UtilsContainer struct {
	Message    MessageUtilsInterface
	Validation ValidationUtilsInterface
	Time       TimeUtilsInterface
}

// NewUtilsContainer создает новый контейнер утилит
func NewUtilsContainer(timezone string) *UtilsContainer {
	return &UtilsContainer{
		Message:    NewMessageUtils(),
		Validation: NewValidationUtils(),
		Time:       NewTimeUtils(timezone),
	}
}

// Убеждаемся, что все утилиты реализуют свои интерфейсы
var _ MessageUtilsInterface = (*MessageUtils)(nil)
var _ ValidationUtilsInterface = (*ValidationUtils)(nil)
var _ TimeUtilsInterface = (*TimeUtils)(nil)
