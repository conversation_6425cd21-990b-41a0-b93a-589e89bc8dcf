package utils

import (
	"context"
	"log/slog"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// MessageUtils предоставляет утилиты для работы с сообщениями
type MessageUtils struct{}

// NewMessageUtils создает новый экземпляр MessageUtils
func NewMessageUtils() *MessageUtils {
	return &MessageUtils{}
}

// SendMessage отправляет сообщение с обработкой ошибок
func (m *MessageUtils) SendMessage(ctx context.Context, b *bot.Bot, params *bot.SendMessageParams) (*models.Message, error) {
	msg, err := b.SendMessage(ctx, params)
	if err != nil {
		slog.Error("Failed to send message", "chat_id", params.ChatID, "error", err)
		return nil, err
	}
	return msg, nil
}

// EditMessage редактирует сообщение с обработкой ошибок
func (m *MessageUtils) EditMessage(ctx context.Context, b *bot.Bot, params *bot.EditMessageTextParams) (*models.Message, error) {
	msg, err := b.EditMessageText(ctx, params)
	if err != nil {
		slog.Error("Failed to edit message", "chat_id", params.ChatID, "message_id", params.MessageID, "error", err)
		return nil, err
	}
	return msg, nil
}

// DeleteMessage удаляет сообщение с обработкой ошибок
func (m *MessageUtils) DeleteMessage(ctx context.Context, b *bot.Bot, chatID int64, messageID int) error {
	_, err := b.DeleteMessage(ctx, &bot.DeleteMessageParams{
		ChatID:    chatID,
		MessageID: messageID,
	})
	if err != nil {
		slog.Warn("Failed to delete message", "chat_id", chatID, "message_id", messageID, "error", err)
	}
	return err
}

// SendAndSaveMessage отправляет сообщение и сохраняет его ID в кеше
func (m *MessageUtils) SendAndSaveMessage(ctx context.Context, b *bot.Bot, params *bot.SendMessageParams, saveFunc func(int)) (*models.Message, error) {
	msg, err := m.SendMessage(ctx, b, params)
	if err != nil {
		return nil, err
	}
	
	if saveFunc != nil {
		saveFunc(msg.ID)
	}
	
	return msg, nil
}

// EditAndSaveMessage редактирует сообщение и сохраняет его ID в кеше
func (m *MessageUtils) EditAndSaveMessage(ctx context.Context, b *bot.Bot, params *bot.EditMessageTextParams, saveFunc func(int)) (*models.Message, error) {
	msg, err := m.EditMessage(ctx, b, params)
	if err != nil {
		return nil, err
	}
	
	if saveFunc != nil {
		saveFunc(msg.ID)
	}
	
	return msg, nil
}

// SendHTMLMessage отправляет сообщение с HTML разметкой
func (m *MessageUtils) SendHTMLMessage(ctx context.Context, b *bot.Bot, chatID int64, text string, keyboard *models.InlineKeyboardMarkup) (*models.Message, error) {
	params := &bot.SendMessageParams{
		ChatID:    chatID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
	}
	
	if keyboard != nil {
		params.ReplyMarkup = *keyboard
	}
	
	return m.SendMessage(ctx, b, params)
}

// EditHTMLMessage редактирует сообщение с HTML разметкой
func (m *MessageUtils) EditHTMLMessage(ctx context.Context, b *bot.Bot, chatID int64, messageID int, text string, keyboard *models.InlineKeyboardMarkup) (*models.Message, error) {
	params := &bot.EditMessageTextParams{
		ChatID:    chatID,
		MessageID: messageID,
		Text:      text,
		ParseMode: models.ParseModeHTML,
	}
	
	if keyboard != nil {
		params.ReplyMarkup = *keyboard
	}
	
	return m.EditMessage(ctx, b, params)
}

// SendMenuMessage отправляет сообщение с меню
func (m *MessageUtils) SendMenuMessage(ctx context.Context, b *bot.Bot, chatID int64, text string, keyboard [][]models.InlineKeyboardButton) (*models.Message, error) {
	return m.SendHTMLMessage(ctx, b, chatID, text, &models.InlineKeyboardMarkup{
		InlineKeyboard: keyboard,
	})
}

// EditMenuMessage редактирует сообщение с меню
func (m *MessageUtils) EditMenuMessage(ctx context.Context, b *bot.Bot, chatID int64, messageID int, text string, keyboard [][]models.InlineKeyboardButton) (*models.Message, error) {
	return m.EditHTMLMessage(ctx, b, chatID, messageID, text, &models.InlineKeyboardMarkup{
		InlineKeyboard: keyboard,
	})
}

// DeleteMessageSafely удаляет сообщение без логирования ошибок (для случаев, когда сообщение может уже не существовать)
func (m *MessageUtils) DeleteMessageSafely(ctx context.Context, b *bot.Bot, chatID int64, messageID int) {
	b.DeleteMessage(ctx, &bot.DeleteMessageParams{
		ChatID:    chatID,
		MessageID: messageID,
	})
	// Намеренно игнорируем ошибки
}

// DeleteMultipleMessages удаляет несколько сообщений
func (m *MessageUtils) DeleteMultipleMessages(ctx context.Context, b *bot.Bot, chatID int64, messageIDs []int) {
	for _, messageID := range messageIDs {
		m.DeleteMessageSafely(ctx, b, chatID, messageID)
	}
}

// AnswerCallbackQuery отвечает на callback query
func (m *MessageUtils) AnswerCallbackQuery(ctx context.Context, b *bot.Bot, callbackQueryID string, text string, showAlert bool) error {
	_, err := b.AnswerCallbackQuery(ctx, &bot.AnswerCallbackQueryParams{
		CallbackQueryID: callbackQueryID,
		Text:            text,
		ShowAlert:       showAlert,
	})
	if err != nil {
		slog.Error("Failed to answer callback query", "callback_query_id", callbackQueryID, "error", err)
	}
	return err
}

// SendSuccessMessage отправляет сообщение об успехе
func (m *MessageUtils) SendSuccessMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error) {
	text := "✅ " + message
	var keyboard *models.InlineKeyboardMarkup
	
	if backButton != nil {
		keyboard = &models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{{*backButton}},
		}
	}
	
	return m.SendHTMLMessage(ctx, b, chatID, text, keyboard)
}

// SendErrorMessage отправляет сообщение об ошибке
func (m *MessageUtils) SendErrorMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error) {
	text := "❌ " + message
	var keyboard *models.InlineKeyboardMarkup
	
	if backButton != nil {
		keyboard = &models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{{*backButton}},
		}
	}
	
	return m.SendHTMLMessage(ctx, b, chatID, text, keyboard)
}

// SendWarningMessage отправляет предупреждающее сообщение
func (m *MessageUtils) SendWarningMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error) {
	text := "⚠️ " + message
	var keyboard *models.InlineKeyboardMarkup
	
	if backButton != nil {
		keyboard = &models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{{*backButton}},
		}
	}
	
	return m.SendHTMLMessage(ctx, b, chatID, text, keyboard)
}

// SendInfoMessage отправляет информационное сообщение
func (m *MessageUtils) SendInfoMessage(ctx context.Context, b *bot.Bot, chatID int64, message string, backButton *models.InlineKeyboardButton) (*models.Message, error) {
	text := "ℹ️ " + message
	var keyboard *models.InlineKeyboardMarkup
	
	if backButton != nil {
		keyboard = &models.InlineKeyboardMarkup{
			InlineKeyboard: [][]models.InlineKeyboardButton{{*backButton}},
		}
	}
	
	return m.SendHTMLMessage(ctx, b, chatID, text, keyboard)
}
