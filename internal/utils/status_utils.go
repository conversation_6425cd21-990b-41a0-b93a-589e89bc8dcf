package utils

import "remnawave-tg-shop-bot/internal/database"

// StatusUtils предоставляет утилиты для работы со статусами
type StatusUtils struct{}

// NewStatusUtils создает новый экземпляр StatusUtils
func NewStatusUtils() *StatusUtils {
	return &StatusUtils{}
}

// TranslateStatus переводит статус рассылки
func (su *StatusUtils) TranslateStatus(status database.BroadcastTaskStatus) string {
	switch status {
	case database.BroadcastTaskStatusPending:
		return "В ожидании"
	case database.BroadcastTaskStatusSent:
		return "Отправлено"
	case database.BroadcastTaskStatusCancelled:
		return "Отменено"
	default:
		return string(status)
	}
}
