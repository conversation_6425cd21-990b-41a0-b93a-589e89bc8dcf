package utils

import (
	"fmt"
	"time"
)

// TimeUtils предоставляет утилиты для работы с временем
type TimeUtils struct {
	defaultTimezone string
}

// NewTimeUtils создает новый экземпляр TimeUtils
func NewTimeUtils(defaultTimezone string) *TimeUtils {
	return &TimeUtils{
		defaultTimezone: defaultTimezone,
	}
}

// GetLocation возвращает часовой пояс
func (t *TimeUtils) GetLocation() *time.Location {
	loc, err := time.LoadLocation(t.defaultTimezone)
	if err != nil {
		return time.UTC
	}
	return loc
}

// Now возвращает текущее время в настроенном часовом поясе
func (t *TimeUtils) Now() time.Time {
	return time.Now().In(t.GetLocation())
}

// FormatDateTime форматирует время в читаемый формат
func (t *TimeUtils) FormatDateTime(tm time.Time) string {
	loc := t.GetLocation()
	localTime := tm.In(loc)
	return localTime.Format("02.01.2006 15:04")
}

// FormatDateTimeWithTZ форматирует время с указанием часового пояса
func (t *TimeUtils) FormatDateTimeWithTZ(tm time.Time) string {
	loc := t.GetLocation()
	localTime := tm.In(loc)
	_, offset := localTime.Zone()
	hours := offset / 3600
	minutes := (offset % 3600) / 60
	
	var sign string
	if hours >= 0 {
		sign = "+"
	} else {
		sign = "-"
		hours = -hours
		minutes = -minutes
	}
	
	return localTime.Format("02.01.2006 15:04") + fmt.Sprintf(" (UTC%s%02d:%02d)", sign, hours, minutes)
}

// ParseDateTime парсит строку даты и времени
func (t *TimeUtils) ParseDateTime(dateTimeStr string) (time.Time, error) {
	loc := t.GetLocation()
	
	// Пробуем различные форматы
	formats := []string{
		"02.01.2006 15:04",
		"2.1.2006 15:04",
		"02.01.2006 15:4",
		"2.1.2006 15:4",
		"02.01.06 15:04",
		"2.1.06 15:04",
		"02.01.2006 15:04:05",
		"2006-01-02 15:04",
		"2006-01-02 15:04:05",
	}
	
	for _, format := range formats {
		if parsedTime, err := time.ParseInLocation(format, dateTimeStr, loc); err == nil {
			return parsedTime, nil
		}
	}
	
	return time.Time{}, fmt.Errorf("неверный формат даты и времени: %s", dateTimeStr)
}

// IsInPast проверяет, находится ли время в прошлом
func (t *TimeUtils) IsInPast(tm time.Time) bool {
	return tm.Before(t.Now())
}

// IsInFuture проверяет, находится ли время в будущем
func (t *TimeUtils) IsInFuture(tm time.Time) bool {
	return tm.After(t.Now())
}

// AddDuration добавляет продолжительность к времени
func (t *TimeUtils) AddDuration(tm time.Time, duration time.Duration) time.Time {
	return tm.Add(duration)
}

// DurationUntil возвращает продолжительность до указанного времени
func (t *TimeUtils) DurationUntil(tm time.Time) time.Duration {
	return tm.Sub(t.Now())
}

// FormatDuration форматирует продолжительность в читаемый вид
func (t *TimeUtils) FormatDuration(d time.Duration) string {
	if d < 0 {
		return "прошло"
	}
	
	days := int(d.Hours()) / 24
	hours := int(d.Hours()) % 24
	minutes := int(d.Minutes()) % 60
	
	if days > 0 {
		return fmt.Sprintf("%d дн. %d ч. %d мин.", days, hours, minutes)
	} else if hours > 0 {
		return fmt.Sprintf("%d ч. %d мин.", hours, minutes)
	} else if minutes > 0 {
		return fmt.Sprintf("%d мин.", minutes)
	} else {
		return "менее минуты"
	}
}

// GetTimeUntilString возвращает строку с временем до указанного момента
func (t *TimeUtils) GetTimeUntilString(tm time.Time) string {
	duration := t.DurationUntil(tm)
	if duration < 0 {
		return "время прошло"
	}
	return "через " + t.FormatDuration(duration)
}

// IsToday проверяет, является ли дата сегодняшней
func (t *TimeUtils) IsToday(tm time.Time) bool {
	now := t.Now()
	return tm.Year() == now.Year() && tm.YearDay() == now.YearDay()
}

// IsTomorrow проверяет, является ли дата завтрашней
func (t *TimeUtils) IsTomorrow(tm time.Time) bool {
	tomorrow := t.Now().AddDate(0, 0, 1)
	return tm.Year() == tomorrow.Year() && tm.YearDay() == tomorrow.YearDay()
}

// IsYesterday проверяет, является ли дата вчерашней
func (t *TimeUtils) IsYesterday(tm time.Time) bool {
	yesterday := t.Now().AddDate(0, 0, -1)
	return tm.Year() == yesterday.Year() && tm.YearDay() == yesterday.YearDay()
}

// FormatRelativeDate форматирует дату относительно текущего дня
func (t *TimeUtils) FormatRelativeDate(tm time.Time) string {
	if t.IsToday(tm) {
		return "сегодня в " + tm.In(t.GetLocation()).Format("15:04")
	} else if t.IsTomorrow(tm) {
		return "завтра в " + tm.In(t.GetLocation()).Format("15:04")
	} else if t.IsYesterday(tm) {
		return "вчера в " + tm.In(t.GetLocation()).Format("15:04")
	} else {
		return t.FormatDateTime(tm)
	}
}

// StartOfDay возвращает начало дня для указанного времени
func (t *TimeUtils) StartOfDay(tm time.Time) time.Time {
	loc := t.GetLocation()
	localTime := tm.In(loc)
	return time.Date(localTime.Year(), localTime.Month(), localTime.Day(), 0, 0, 0, 0, loc)
}

// EndOfDay возвращает конец дня для указанного времени
func (t *TimeUtils) EndOfDay(tm time.Time) time.Time {
	loc := t.GetLocation()
	localTime := tm.In(loc)
	return time.Date(localTime.Year(), localTime.Month(), localTime.Day(), 23, 59, 59, 999999999, loc)
}

// GetWeekday возвращает день недели на русском языке
func (t *TimeUtils) GetWeekday(tm time.Time) string {
	weekdays := map[time.Weekday]string{
		time.Monday:    "понедельник",
		time.Tuesday:   "вторник",
		time.Wednesday: "среда",
		time.Thursday:  "четверг",
		time.Friday:    "пятница",
		time.Saturday:  "суббота",
		time.Sunday:    "воскресенье",
	}
	
	return weekdays[tm.Weekday()]
}

// GetMonth возвращает месяц на русском языке
func (t *TimeUtils) GetMonth(tm time.Time) string {
	months := map[time.Month]string{
		time.January:   "январь",
		time.February:  "февраль",
		time.March:     "март",
		time.April:     "апрель",
		time.May:       "май",
		time.June:      "июнь",
		time.July:      "июль",
		time.August:    "август",
		time.September: "сентябрь",
		time.October:   "октябрь",
		time.November:  "ноябрь",
		time.December:  "декабрь",
	}
	
	return months[tm.Month()]
}

// FormatFullDate форматирует полную дату с днем недели и месяцем на русском
func (t *TimeUtils) FormatFullDate(tm time.Time) string {
	localTime := tm.In(t.GetLocation())
	return fmt.Sprintf("%s, %d %s %d года в %s",
		t.GetWeekday(localTime),
		localTime.Day(),
		t.GetMonth(localTime),
		localTime.Year(),
		localTime.Format("15:04"),
	)
}

// ValidateScheduleTime проверяет, что время подходит для планирования
func (t *TimeUtils) ValidateScheduleTime(scheduleTime time.Time) error {
	now := t.Now()
	
	if scheduleTime.Before(now) {
		return fmt.Errorf("время планирования не может быть в прошлом")
	}
	
	// Проверяем, что время не слишком далеко в будущем (максимум 1 год)
	maxFutureTime := now.AddDate(1, 0, 0)
	if scheduleTime.After(maxFutureTime) {
		return fmt.Errorf("время планирования не может быть более чем через год")
	}
	
	return nil
}
