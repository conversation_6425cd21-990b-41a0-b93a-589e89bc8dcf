package utils

import (
	"fmt"
	"time"

	"remnawave-tg-shop-bot/internal/config"
)

// TimeUtils предоставляет утилиты для работы со временем
type TimeUtils struct{}

// NewTimeUtils создает новый экземпляр TimeUtils
func NewTimeUtils() *TimeUtils {
	return &TimeUtils{}
}

// FormatTimeWithTZ форматирует время с учётом часового пояса из конфига
func (tu *TimeUtils) FormatTimeWithTZ(t time.Time) string {
	loc, err := time.LoadLocation(config.TimeZone())
	if err != nil {
		loc = time.FixedZone("UTC+3", 3*60*60) // fallback
	}
	tInLoc := t.In(loc)
	_, offset := tInLoc.Zone()
	hours := offset / 3600
	var sign string
	if hours >= 0 {
		sign = "+"
	} else {
		sign = "-"
	}
	return tInLoc.Format("02.01.2006 15:04") +
		fmt.Sprintf(" (UTC%s%02d)", sign, tu.abs(hours))
}

// FormatTimeWithTZMustParse форматирует время из строки с учётом часового пояса
func (tu *TimeUtils) FormatTimeWithTZMustParse(timeStr string) string {
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return timeStr
	}
	return tu.FormatTimeWithTZ(t)
}

// FormatCurrentTZLabel возвращает строку вида UTC+03:00 для текущего пояса
func (tu *TimeUtils) FormatCurrentTZLabel() string {
	loc, err := time.LoadLocation(config.TimeZone())
	if err != nil {
		return "UTC+03:00"
	}
	// Используем 1 января, чтобы не зависеть от перехода на летнее/зимнее время
	t := time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, loc)
	_, offset := t.Zone()
	hours := offset / 3600
	minutes := (offset % 3600) / 60
	return fmt.Sprintf("UTC%+03d:%02d", hours, minutes)
}

// abs возвращает абсолютное значение числа
func (tu *TimeUtils) abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}
