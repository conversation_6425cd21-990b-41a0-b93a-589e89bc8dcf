package utils

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"
)

// ValidationUtils предоставляет утилиты для валидации данных
type ValidationUtils struct{}

// NewValidationUtils создает новый экземпляр ValidationUtils
func NewValidationUtils() *ValidationUtils {
	return &ValidationUtils{}
}

// ValidateNotEmpty проверяет, что строка не пустая
func (v *ValidationUtils) ValidateNotEmpty(value, fieldName string) error {
	if strings.TrimSpace(value) == "" {
		return fmt.Errorf("%s не может быть пустым", fieldName)
	}
	return nil
}

// ValidateLength проверяет длину строки
func (v *ValidationUtils) ValidateLength(value, fieldName string, minLength, maxLength int) error {
	length := utf8.RuneCountInString(value)
	if length < minLength {
		return fmt.Errorf("%s должно содержать минимум %d символов", fieldName, minLength)
	}
	if length > maxLength {
		return fmt.Errorf("%s не может содержать более %d символов", fieldName, maxLength)
	}
	return nil
}

// ValidatePositiveInt проверяет, что число положительное
func (v *ValidationUtils) ValidatePositiveInt(value int, fieldName string) error {
	if value <= 0 {
		return fmt.Errorf("%s должно быть положительным числом", fieldName)
	}
	return nil
}

// ValidateIntRange проверяет, что число находится в заданном диапазоне
func (v *ValidationUtils) ValidateIntRange(value int, fieldName string, min, max int) error {
	if value < min || value > max {
		return fmt.Errorf("%s должно быть в диапазоне от %d до %d", fieldName, min, max)
	}
	return nil
}

// ValidateRegex проверяет строку по регулярному выражению
func (v *ValidationUtils) ValidateRegex(value, fieldName, pattern, errorMessage string) error {
	matched, err := regexp.MatchString(pattern, value)
	if err != nil {
		return fmt.Errorf("ошибка валидации %s: %w", fieldName, err)
	}
	if !matched {
		return fmt.Errorf("%s: %s", fieldName, errorMessage)
	}
	return nil
}

// ValidateTariffCode проверяет код тарифа
func (v *ValidationUtils) ValidateTariffCode(code string) error {
	if err := v.ValidateNotEmpty(code, "код тарифа"); err != nil {
		return err
	}
	
	if err := v.ValidateLength(code, "код тарифа", 1, 32); err != nil {
		return err
	}
	
	return v.ValidateRegex(code, "код тарифа", "^[a-zA-Z0-9_-]+$", "может содержать только латинские буквы, цифры, подчеркивания и дефисы")
}

// ValidateTariffTitle проверяет название тарифа
func (v *ValidationUtils) ValidateTariffTitle(title string) error {
	if err := v.ValidateNotEmpty(title, "название тарифа"); err != nil {
		return err
	}
	
	return v.ValidateLength(title, "название тарифа", 1, 64)
}

// ValidateTariffPrice проверяет цену тарифа
func (v *ValidationUtils) ValidateTariffPrice(price int, currency string) error {
	if err := v.ValidatePositiveInt(price, fmt.Sprintf("цена в %s", currency)); err != nil {
		return err
	}
	
	var maxPrice int
	switch currency {
	case "RUB":
		maxPrice = 1000000 // 1 миллион рублей
	case "Stars":
		maxPrice = 10000 // 10 тысяч звезд
	default:
		maxPrice = 1000000
	}
	
	return v.ValidateIntRange(price, fmt.Sprintf("цена в %s", currency), 1, maxPrice)
}

// ValidateBroadcastText проверяет текст рассылки
func (v *ValidationUtils) ValidateBroadcastText(text string) error {
	if err := v.ValidateNotEmpty(text, "текст рассылки"); err != nil {
		return err
	}
	
	return v.ValidateLength(text, "текст рассылки", 1, 4096) // Лимит Telegram
}

// ValidateDateTime проверяет и парсит дату и время
func (v *ValidationUtils) ValidateDateTime(dateTimeStr, timezone string) (time.Time, error) {
	if err := v.ValidateNotEmpty(dateTimeStr, "дата и время"); err != nil {
		return time.Time{}, err
	}
	
	// Загружаем часовой пояс
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		loc = time.UTC
	}
	
	// Пробуем различные форматы
	formats := []string{
		"02.01.2006 15:04",
		"2.1.2006 15:04",
		"02.01.2006 15:4",
		"2.1.2006 15:4",
		"02.01.06 15:04",
		"2.1.06 15:04",
	}
	
	var parsedTime time.Time
	var parseErr error
	
	for _, format := range formats {
		parsedTime, parseErr = time.ParseInLocation(format, dateTimeStr, loc)
		if parseErr == nil {
			break
		}
	}
	
	if parseErr != nil {
		return time.Time{}, fmt.Errorf("неверный формат даты и времени. Используйте формат: ДД.ММ.ГГГГ ЧЧ:ММ")
	}
	
	// Проверяем, что время не в прошлом
	now := time.Now().In(loc)
	if parsedTime.Before(now) {
		return time.Time{}, fmt.Errorf("время отправки не может быть в прошлом")
	}
	
	// Проверяем, что время не слишком далеко в будущем (максимум 1 год)
	maxFutureTime := now.AddDate(1, 0, 0)
	if parsedTime.After(maxFutureTime) {
		return time.Time{}, fmt.Errorf("время отправки не может быть более чем через год")
	}
	
	return parsedTime, nil
}

// ValidateUserID проверяет ID пользователя Telegram
func (v *ValidationUtils) ValidateUserID(userID int64) error {
	if userID <= 0 {
		return fmt.Errorf("неверный ID пользователя: %d", userID)
	}
	return nil
}

// ValidateMessageID проверяет ID сообщения
func (v *ValidationUtils) ValidateMessageID(messageID int) error {
	if messageID <= 0 {
		return fmt.Errorf("неверный ID сообщения: %d", messageID)
	}
	return nil
}

// ValidateCallbackData проверяет данные callback
func (v *ValidationUtils) ValidateCallbackData(data string) error {
	if err := v.ValidateNotEmpty(data, "callback данные"); err != nil {
		return err
	}
	
	return v.ValidateLength(data, "callback данные", 1, 64) // Лимит Telegram
}

// ParsePositiveInt парсит строку в положительное число
func (v *ValidationUtils) ParsePositiveInt(s, fieldName string) (int, error) {
	if err := v.ValidateNotEmpty(s, fieldName); err != nil {
		return 0, err
	}
	
	value, err := strconv.Atoi(strings.TrimSpace(s))
	if err != nil {
		return 0, fmt.Errorf("%s должно быть числом", fieldName)
	}
	
	if err := v.ValidatePositiveInt(value, fieldName); err != nil {
		return 0, err
	}
	
	return value, nil
}

// ValidateAudience проверяет тип аудитории для рассылки
func (v *ValidationUtils) ValidateAudience(audience string) error {
	validAudiences := []string{"all", "active", "inactive"}
	
	for _, valid := range validAudiences {
		if audience == valid {
			return nil
		}
	}
	
	return fmt.Errorf("неверный тип аудитории: %s. Допустимые значения: %s", audience, strings.Join(validAudiences, ", "))
}

// ValidatePageNumber проверяет номер страницы для пагинации
func (v *ValidationUtils) ValidatePageNumber(page int) error {
	if page < 1 {
		return fmt.Errorf("номер страницы должен быть больше 0")
	}
	return nil
}

// SanitizeString очищает строку от лишних пробелов и управляющих символов
func (v *ValidationUtils) SanitizeString(s string) string {
	// Убираем пробелы в начале и конце
	s = strings.TrimSpace(s)
	
	// Заменяем множественные пробелы на одинарные
	re := regexp.MustCompile(`\s+`)
	s = re.ReplaceAllString(s, " ")
	
	return s
}

// ValidateAndSanitizeString проверяет и очищает строку
func (v *ValidationUtils) ValidateAndSanitizeString(value, fieldName string, minLength, maxLength int) (string, error) {
	sanitized := v.SanitizeString(value)
	
	if err := v.ValidateNotEmpty(sanitized, fieldName); err != nil {
		return "", err
	}
	
	if err := v.ValidateLength(sanitized, fieldName, minLength, maxLength); err != nil {
		return "", err
	}
	
	return sanitized, nil
}
